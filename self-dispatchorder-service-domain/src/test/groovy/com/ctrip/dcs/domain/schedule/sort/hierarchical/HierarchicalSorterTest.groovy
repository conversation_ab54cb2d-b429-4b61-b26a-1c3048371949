package com.ctrip.dcs.domain.schedule.sort.hierarchical

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.feature.Feature
import com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator.CategoryScoreCalculator
import com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator.GradeCalculator
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.CategoryScore
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.HierarchicalSortResult
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

/**
 * 分层排序器测试类
 * 测试分层排序的核心功能
 * 
 * <AUTHOR>
 */
class HierarchicalSorterTest extends Specification {
    
    HierarchicalSorter hierarchicalSorter
    CategoryScoreCalculator categoryScoreCalculator
    GradeCalculator gradeCalculator
    
    def setup() {
        categoryScoreCalculator = Mock(CategoryScoreCalculator)
        gradeCalculator = Mock(GradeCalculator)
        
        hierarchicalSorter = new HierarchicalSorter()
        hierarchicalSorter.categoryScoreCalculator = categoryScoreCalculator
        hierarchicalSorter.gradeCalculator = gradeCalculator
    }
    
    def "测试空司机列表排序"() {
        given: "空的司机列表"
        List<SortModel> sortModels = []
        SortContext sortContext = Mock(SortContext)
        HierarchicalSortConfig config = Mock(HierarchicalSortConfig)
        SortPreference preference = SortPreference.BALANCED
        Map<String, Feature> featureMap = [:]
        
        when: "执行排序"
        List<HierarchicalSortResult> results = hierarchicalSorter.sort(
            sortModels, sortContext, config, preference, featureMap)
        
        then: "返回空结果"
        results.isEmpty()
    }
    
    def "测试分层排序功能已禁用"() {
        given: "禁用的配置"
        List<SortModel> sortModels = createMockSortModels(2)
        SortContext sortContext = Mock(SortContext)
        HierarchicalSortConfig config = Mock(HierarchicalSortConfig)
        config.isEffectivelyEnabled() >> false
        SortPreference preference = SortPreference.BALANCED
        Map<String, Feature> featureMap = [:]
        
        when: "执行排序"
        List<HierarchicalSortResult> results = hierarchicalSorter.sort(
            sortModels, sortContext, config, preference, featureMap)
        
        then: "返回空结果"
        results.isEmpty()
    }
    
    def "测试正常的分层排序流程"() {
        given: "正常的输入参数"
        List<SortModel> sortModels = createMockSortModels(3)
        SortContext sortContext = Mock(SortContext)
        HierarchicalSortConfig config = createMockConfig()
        SortPreference preference = SortPreference.BALANCED
        Map<String, Feature> featureMap = [:]
        
        and: "模拟计算器返回结果"
        List<CategoryScore> categoryScores = createMockCategoryScores()
        categoryScoreCalculator.calculateCategoryScores(_, _, _, _) >> categoryScores
        gradeCalculator.calculateOverallGpaScore(_, _, _) >> 3.2
        gradeCalculator.calculateOverallGrade(3.2) >> SortGrade.B
        gradeCalculator.calculateFinalSortScore(_, _, _, _) >> 82.5
        
        when: "执行排序"
        List<HierarchicalSortResult> results = hierarchicalSorter.sort(
            sortModels, sortContext, config, preference, featureMap)
        
        then: "返回正确的结果"
        results.size() == 3
        results.each { result ->
            assert result.isEffectivelySuccess()
            assert result.getEffectiveOverallGrade() == SortGrade.B
            assert result.getEffectiveOverallGpaScore() == 3.2
            assert result.getEffectiveFinalSortScore() == 82.5
        }
        
        and: "结果按分数排序"
        for (int i = 0; i < results.size() - 1; i++) {
            assert results[i].getEffectiveFinalSortScore() >= results[i + 1].getEffectiveFinalSortScore()
        }
    }
    
    def "测试排序结果的排名设置"() {
        given: "多个司机的排序结果"
        List<SortModel> sortModels = createMockSortModels(5)
        SortContext sortContext = Mock(SortContext)
        HierarchicalSortConfig config = createMockConfig()
        SortPreference preference = SortPreference.BALANCED
        Map<String, Feature> featureMap = [:]
        
        and: "模拟不同的分数"
        List<CategoryScore> categoryScores = createMockCategoryScores()
        categoryScoreCalculator.calculateCategoryScores(_, _, _, _) >> categoryScores
        gradeCalculator.calculateOverallGpaScore(_, _, _) >>> [3.8, 3.2, 2.8, 2.1, 1.5]
        gradeCalculator.calculateOverallGrade(_) >>> [SortGrade.A, SortGrade.B, SortGrade.B, SortGrade.C, SortGrade.D]
        gradeCalculator.calculateFinalSortScore(_, _, _, _) >>> [92.0, 82.0, 78.0, 65.0, 45.0]
        
        when: "执行排序"
        List<HierarchicalSortResult> results = hierarchicalSorter.sort(
            sortModels, sortContext, config, preference, featureMap)
        
        then: "排名正确设置"
        results.size() == 5
        results[0].getEffectiveRank() == 1
        results[1].getEffectiveRank() == 2
        results[2].getEffectiveRank() == 3
        results[3].getEffectiveRank() == 4
        results[4].getEffectiveRank() == 5
        
        and: "分数按降序排列"
        results[0].getEffectiveFinalSortScore() == 92.0
        results[1].getEffectiveFinalSortScore() == 82.0
        results[2].getEffectiveFinalSortScore() == 78.0
        results[3].getEffectiveFinalSortScore() == 65.0
        results[4].getEffectiveFinalSortScore() == 45.0
    }
    
    def "测试异常处理"() {
        given: "会抛出异常的计算器"
        List<SortModel> sortModels = createMockSortModels(2)
        SortContext sortContext = Mock(SortContext)
        HierarchicalSortConfig config = createMockConfig()
        SortPreference preference = SortPreference.BALANCED
        Map<String, Feature> featureMap = [:]
        
        and: "计算器抛出异常"
        categoryScoreCalculator.calculateCategoryScores(_, _, _, _) >> { throw new RuntimeException("计算异常") }
        
        when: "执行排序"
        List<HierarchicalSortResult> results = hierarchicalSorter.sort(
            sortModels, sortContext, config, preference, featureMap)
        
        then: "返回空结果，不抛出异常"
        results.isEmpty()
    }
    
    /**
     * 创建模拟的SortModel列表
     */
    private List<SortModel> createMockSortModels(int count) {
        List<SortModel> models = []
        for (int i = 1; i <= count; i++) {
            DriverVO driver = Mock(DriverVO)
            driver.getDriverId() >> (long) i
            
            DspOrderVO order = Mock(DspOrderVO)
            order.getDspOrderId() >> "order_${i}"
            
            DspModelVO dspModel = new DspModelVO(order, driver)
            models.add(new SortModel(dspModel))
        }
        return models
    }
    
    /**
     * 创建模拟的配置
     */
    private HierarchicalSortConfig createMockConfig() {
        HierarchicalSortConfig config = Mock(HierarchicalSortConfig)
        config.isEffectivelyEnabled() >> true
        config.getCategoryConfigs() >> []
        config.getCustomWeights() >> [:]
        config.getGlobalTimeoutMs() >> 10000L
        return config
    }
    
    /**
     * 创建模拟的大类分数
     */
    private List<CategoryScore> createMockCategoryScores() {
        return [
            CategoryScore.createSuccess(
                com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory.TIME_SPACE_EFFICIENCY,
                80.0, SortGrade.B, 0.3, [], 100L),
            CategoryScore.createSuccess(
                com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory.SERVICE_QUALITY,
                85.0, SortGrade.A, 0.3, [], 100L),
            CategoryScore.createSuccess(
                com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory.ORDER_MATCHING,
                75.0, SortGrade.B, 0.25, [], 100L),
            CategoryScore.createSuccess(
                com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory.GLOBAL_EFFICIENCY,
                70.0, SortGrade.B, 0.15, [], 100L)
        ]
    }
}
