# 分层排序配置文件
# 该配置文件定义了分层排序系统的所有配置参数

# 基本配置
version: "1.0.0"
configName: "default"
enabled: true
defaultPreference: "BALANCED"
globalTimeoutMs: 10000
maxConcurrency: 100

# 城市灰度配置
cityGrayConfig:
  enabled: true
  # 启用分层排序的城市ID列表
  enabledCities: [1, 2, 289, 565, 7, 8, 9, 10]
  # 灰度比例（0-100，表示百分比）
  grayPercentage: 10
  # 白名单司机ID（强制使用新排序）
  whitelistDrivers: []
  # 黑名单司机ID（强制使用旧排序）
  blacklistDrivers: []

# 监控配置
monitorConfig:
  enabled: true
  detailedLogging: false
  performanceMonitoring: true
  resultComparison: true
  # 监控采样率（0-100，表示百分比）
  samplingRate: 10

# 降级配置
fallbackConfig:
  enabled: true
  # 超时降级阈值（毫秒）
  timeoutThresholdMs: 5000
  # 异常降级阈值（连续异常次数）
  exceptionThreshold: 3
  # 降级恢复时间（毫秒）
  recoveryTimeMs: 60000
  # 是否启用熔断器
  circuitBreakerEnabled: true
  # 熔断器失败率阈值（0-100，表示百分比）
  failureRateThreshold: 50

# 大类配置
categoryConfigs:
  # 时空效率类
  - category: "TIME_SPACE_EFFICIENCY"
    weight: 0.30
    enabled: true
    description: "时空效率类指标，关注司机的空驶时间和距离"
    timeoutMs: 5000
    isCoreCategory: true
    defaultGrade: "C"
    # 等级阈值配置
    gradeThresholds:
      A: 85.0
      B: 70.0
      C: 50.0
      D: 0.0
    # 子项配置
    subItems:
      - itemId: "F9"
        displayName: "局部时间间隔"
        weight: 0.25
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: 0.0
        normalizeParam2: 210.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F10"
        displayName: "局部空驶距离"
        weight: 0.25
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: -20.0
        normalizeParam2: 0.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F21"
        displayName: "司机空驶时长"
        weight: 0.20
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: 0.0
        normalizeParam2: 3600.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F6"
        displayName: "空驶距离"
        weight: 0.15
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: 0.0
        normalizeParam2: 50000.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F4"
        displayName: "接驾时间成本"
        weight: 0.15
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1800.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0

  # 服务质量类
  - category: "SERVICE_QUALITY"
    weight: 0.30
    enabled: true
    description: "服务质量类指标，关注司机的服务水平和分层"
    timeoutMs: 5000
    isCoreCategory: true
    defaultGrade: "C"
    gradeThresholds:
      A: 85.0
      B: 70.0
      C: 50.0
      D: 0.0
    subItems:
      - itemId: "F2"
        displayName: "司机分占比"
        weight: 0.35
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F19"
        displayName: "司机分层"
        weight: 0.30
        normalizationType: "LOG_NORMALIZE"
        normalizeParam1: 1.0
        normalizeParam2: 10000.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 1.0
      - itemId: "F1"
        displayName: "司机分"
        weight: 0.20
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1000.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F40"
        displayName: "司机分类"
        weight: 0.15
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 10.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0

  # 订单匹配度类
  - category: "ORDER_MATCHING"
    weight: 0.25
    enabled: true
    description: "订单匹配度类指标，关注订单与司机的匹配程度"
    timeoutMs: 5000
    isCoreCategory: true
    defaultGrade: "C"
    gradeThresholds:
      A: 85.0
      B: 70.0
      C: 50.0
      D: 0.0
    subItems:
      - itemId: "F14"
        displayName: "里程价值"
        weight: 0.35
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 10.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F11"
        displayName: "未来接单能力"
        weight: 0.30
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 10.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F41"
        displayName: "订单衔接"
        weight: 0.20
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 100.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F39"
        displayName: "临近单优先"
        weight: 0.15
        normalizationType: "MIN_MAX_NEGATIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1800.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0

  # 全局效率类
  - category: "GLOBAL_EFFICIENCY"
    weight: 0.15
    enabled: true
    description: "全局效率类指标，关注收益均衡和全局优化"
    timeoutMs: 5000
    isCoreCategory: true
    defaultGrade: "C"
    gradeThresholds:
      A: 85.0
      B: 70.0
      C: 50.0
      D: 0.0
    subItems:
      - itemId: "F13"
        displayName: "司机日收益"
        weight: 0.40
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: true
        defaultValue: 0.0
      - itemId: "F12"
        displayName: "司机月收益"
        weight: 0.25
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 1.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F17"
        displayName: "订单分流"
        weight: 0.20
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 100.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0
      - itemId: "F42"
        displayName: "高峰时段"
        weight: 0.15
        normalizationType: "MIN_MAX_POSITIVE"
        normalizeParam1: 0.0
        normalizeParam2: 10.0
        enabled: true
        timeoutMs: 1000
        isCoreMetric: false
        defaultValue: 0.0

# 自定义权重配置（用于CUSTOM偏好）
customWeights:
  TIME_SPACE_EFFICIENCY: 0.25
  SERVICE_QUALITY: 0.25
  ORDER_MATCHING: 0.25
  GLOBAL_EFFICIENCY: 0.25
