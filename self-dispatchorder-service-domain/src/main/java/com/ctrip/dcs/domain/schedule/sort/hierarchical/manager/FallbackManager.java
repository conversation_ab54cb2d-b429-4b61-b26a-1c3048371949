package com.ctrip.dcs.domain.schedule.sort.hierarchical.manager;

import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 降级管理器
 * 负责管理分层排序的降级策略和熔断机制
 * 
 * <AUTHOR>
 */
@Component
public class FallbackManager {
    
    private static final Logger logger = LoggerFactory.getLogger(FallbackManager.class);
    
    /**
     * 城市级别的异常计数器
     */
    private final ConcurrentHashMap<Integer, AtomicInteger> cityExceptionCounters = new ConcurrentHashMap<>();
    
    /**
     * 城市级别的最后异常时间
     */
    private final ConcurrentHashMap<Integer, AtomicLong> cityLastExceptionTime = new ConcurrentHashMap<>();
    
    /**
     * 城市级别的熔断状态
     */
    private final ConcurrentHashMap<Integer, Boolean> cityCircuitBreakerStatus = new ConcurrentHashMap<>();
    
    /**
     * 全局异常计数器
     */
    private final AtomicInteger globalExceptionCounter = new AtomicInteger(0);
    
    /**
     * 全局最后异常时间
     */
    private final AtomicLong globalLastExceptionTime = new AtomicLong(0);
    
    /**
     * 全局熔断状态
     */
    private volatile boolean globalCircuitBreakerOpen = false;
    
    /**
     * 判断是否应该降级到原排序
     * 
     * @param cityId 城市ID
     * @param config 配置信息
     * @return true表示应该降级，false表示可以使用分层排序
     */
    public boolean shouldFallback(Integer cityId, HierarchicalSortConfig config) {
        if (config == null || config.getFallbackConfig() == null || !config.getFallbackConfig().getEnabled()) {
            return false;
        }
        
        HierarchicalSortConfig.FallbackConfig fallbackConfig = config.getFallbackConfig();
        
        try {
            // 检查全局熔断状态
            if (isGlobalCircuitBreakerOpen(fallbackConfig)) {
                logger.warn("全局熔断器开启，降级到原排序");
                return true;
            }
            
            // 检查城市级别熔断状态
            if (isCityCircuitBreakerOpen(cityId, fallbackConfig)) {
                logger.warn("城市{}熔断器开启，降级到原排序", cityId);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("判断是否降级时发生异常", e);
            return true; // 异常时默认降级
        }
    }
    
    /**
     * 记录超时事件
     * 
     * @param cityId 城市ID
     * @param timeoutMs 超时时间
     * @param config 配置信息
     */
    public void recordTimeout(Integer cityId, long timeoutMs, HierarchicalSortConfig config) {
        try {
            logger.warn("分层排序超时：城市={}，耗时={}ms", cityId, timeoutMs);
            
            // 记录监控指标
            MetricsUtil.recordValue("hierarchical_sort_timeout_count");
            MetricsUtil.recordTime("hierarchical_sort_timeout_duration", timeoutMs);
            
            // 超时也视为异常，触发降级逻辑
            recordException(cityId, new RuntimeException("排序超时：" + timeoutMs + "ms"), config);
            
        } catch (Exception e) {
            logger.error("记录超时事件时发生异常", e);
        }
    }
    
    /**
     * 记录异常事件
     * 
     * @param cityId 城市ID
     * @param exception 异常信息
     * @param config 配置信息
     */
    public void recordException(Integer cityId, Exception exception, HierarchicalSortConfig config) {
        try {
            logger.error("分层排序异常：城市={}", cityId, exception);
            
            // 记录监控指标
            MetricsUtil.recordValue("hierarchical_sort_exception_count");
            
            if (config == null || config.getFallbackConfig() == null || !config.getFallbackConfig().getEnabled()) {
                return;
            }
            
            HierarchicalSortConfig.FallbackConfig fallbackConfig = config.getFallbackConfig();
            
            // 更新全局异常计数
            globalExceptionCounter.incrementAndGet();
            globalLastExceptionTime.set(System.currentTimeMillis());
            
            // 更新城市级别异常计数
            if (cityId != null) {
                AtomicInteger cityCounter = cityExceptionCounters.computeIfAbsent(cityId, k -> new AtomicInteger(0));
                cityCounter.incrementAndGet();
                
                AtomicLong cityLastTime = cityLastExceptionTime.computeIfAbsent(cityId, k -> new AtomicLong(0));
                cityLastTime.set(System.currentTimeMillis());
                
                // 检查是否需要开启城市级别熔断器
                checkAndOpenCityCircuitBreaker(cityId, fallbackConfig);
            }
            
            // 检查是否需要开启全局熔断器
            checkAndOpenGlobalCircuitBreaker(fallbackConfig);
            
        } catch (Exception e) {
            logger.error("记录异常事件时发生异常", e);
        }
    }
    
    /**
     * 记录成功事件
     * 
     * @param cityId 城市ID
     * @param config 配置信息
     */
    public void recordSuccess(Integer cityId, HierarchicalSortConfig config) {
        try {
            // 成功时重置异常计数器
            if (cityId != null) {
                AtomicInteger cityCounter = cityExceptionCounters.get(cityId);
                if (cityCounter != null) {
                    cityCounter.set(0);
                }
            }
            
            // 检查是否可以关闭熔断器
            checkAndCloseCityCircuitBreaker(cityId, config);
            checkAndCloseGlobalCircuitBreaker(config);
            
        } catch (Exception e) {
            logger.error("记录成功事件时发生异常", e);
        }
    }
    
    /**
     * 检查全局熔断器状态
     * 
     * @param fallbackConfig 降级配置
     * @return true表示熔断器开启，false表示关闭
     */
    private boolean isGlobalCircuitBreakerOpen(HierarchicalSortConfig.FallbackConfig fallbackConfig) {
        if (!fallbackConfig.getCircuitBreakerEnabled()) {
            return false;
        }
        
        return globalCircuitBreakerOpen;
    }
    
    /**
     * 检查城市级别熔断器状态
     * 
     * @param cityId 城市ID
     * @param fallbackConfig 降级配置
     * @return true表示熔断器开启，false表示关闭
     */
    private boolean isCityCircuitBreakerOpen(Integer cityId, HierarchicalSortConfig.FallbackConfig fallbackConfig) {
        if (!fallbackConfig.getCircuitBreakerEnabled() || cityId == null) {
            return false;
        }
        
        return cityCircuitBreakerStatus.getOrDefault(cityId, false);
    }
    
    /**
     * 检查并开启城市级别熔断器
     * 
     * @param cityId 城市ID
     * @param fallbackConfig 降级配置
     */
    private void checkAndOpenCityCircuitBreaker(Integer cityId, HierarchicalSortConfig.FallbackConfig fallbackConfig) {
        if (!fallbackConfig.getCircuitBreakerEnabled() || cityId == null) {
            return;
        }
        
        AtomicInteger cityCounter = cityExceptionCounters.get(cityId);
        if (cityCounter != null && cityCounter.get() >= fallbackConfig.getExceptionThreshold()) {
            cityCircuitBreakerStatus.put(cityId, true);
            logger.warn("开启城市{}熔断器，异常次数={}", cityId, cityCounter.get());
            
            // 记录监控指标
            MetricsUtil.recordValue("hierarchical_sort_city_circuit_breaker_open_count");
        }
    }
    
    /**
     * 检查并开启全局熔断器
     * 
     * @param fallbackConfig 降级配置
     */
    private void checkAndOpenGlobalCircuitBreaker(HierarchicalSortConfig.FallbackConfig fallbackConfig) {
        if (!fallbackConfig.getCircuitBreakerEnabled()) {
            return;
        }
        
        if (globalExceptionCounter.get() >= fallbackConfig.getExceptionThreshold() * 3) { // 全局阈值更高
            globalCircuitBreakerOpen = true;
            logger.warn("开启全局熔断器，异常次数={}", globalExceptionCounter.get());
            
            // 记录监控指标
            MetricsUtil.recordValue("hierarchical_sort_global_circuit_breaker_open_count");
        }
    }
    
    /**
     * 检查并关闭城市级别熔断器
     * 
     * @param cityId 城市ID
     * @param config 配置信息
     */
    private void checkAndCloseCityCircuitBreaker(Integer cityId, HierarchicalSortConfig config) {
        if (config == null || config.getFallbackConfig() == null || 
            !config.getFallbackConfig().getCircuitBreakerEnabled() || cityId == null) {
            return;
        }
        
        HierarchicalSortConfig.FallbackConfig fallbackConfig = config.getFallbackConfig();
        
        // 检查是否超过恢复时间
        AtomicLong cityLastTime = cityLastExceptionTime.get(cityId);
        if (cityLastTime != null) {
            long timeSinceLastException = System.currentTimeMillis() - cityLastTime.get();
            if (timeSinceLastException >= fallbackConfig.getRecoveryTimeMs()) {
                cityCircuitBreakerStatus.put(cityId, false);
                cityExceptionCounters.put(cityId, new AtomicInteger(0));
                logger.info("关闭城市{}熔断器，恢复时间={}ms", cityId, timeSinceLastException);
                
                // 记录监控指标
                MetricsUtil.recordValue("hierarchical_sort_city_circuit_breaker_close_count");
            }
        }
    }
    
    /**
     * 检查并关闭全局熔断器
     * 
     * @param config 配置信息
     */
    private void checkAndCloseGlobalCircuitBreaker(HierarchicalSortConfig config) {
        if (config == null || config.getFallbackConfig() == null || 
            !config.getFallbackConfig().getCircuitBreakerEnabled()) {
            return;
        }
        
        HierarchicalSortConfig.FallbackConfig fallbackConfig = config.getFallbackConfig();
        
        // 检查是否超过恢复时间
        long timeSinceLastException = System.currentTimeMillis() - globalLastExceptionTime.get();
        if (timeSinceLastException >= fallbackConfig.getRecoveryTimeMs()) {
            globalCircuitBreakerOpen = false;
            globalExceptionCounter.set(0);
            logger.info("关闭全局熔断器，恢复时间={}ms", timeSinceLastException);
            
            // 记录监控指标
            MetricsUtil.recordValue("hierarchical_sort_global_circuit_breaker_close_count");
        }
    }
    
    /**
     * 获取降级统计信息
     * 
     * @return 降级统计信息
     */
    public FallbackStatistics getFallbackStatistics() {
        return FallbackStatistics.builder()
                .globalCircuitBreakerOpen(globalCircuitBreakerOpen)
                .globalExceptionCount(globalExceptionCounter.get())
                .cityCircuitBreakerCount(cityCircuitBreakerStatus.values().stream().mapToInt(open -> open ? 1 : 0).sum())
                .totalCityCount(cityExceptionCounters.size())
                .build();
    }
    
    /**
     * 降级统计信息内部类
     */
    public static class FallbackStatistics {
        private final boolean globalCircuitBreakerOpen;
        private final int globalExceptionCount;
        private final int cityCircuitBreakerCount;
        private final int totalCityCount;
        
        private FallbackStatistics(boolean globalCircuitBreakerOpen, int globalExceptionCount,
                                 int cityCircuitBreakerCount, int totalCityCount) {
            this.globalCircuitBreakerOpen = globalCircuitBreakerOpen;
            this.globalExceptionCount = globalExceptionCount;
            this.cityCircuitBreakerCount = cityCircuitBreakerCount;
            this.totalCityCount = totalCityCount;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        // Getters
        public boolean isGlobalCircuitBreakerOpen() { return globalCircuitBreakerOpen; }
        public int getGlobalExceptionCount() { return globalExceptionCount; }
        public int getCityCircuitBreakerCount() { return cityCircuitBreakerCount; }
        public int getTotalCityCount() { return totalCityCount; }
        
        public static class Builder {
            private boolean globalCircuitBreakerOpen;
            private int globalExceptionCount;
            private int cityCircuitBreakerCount;
            private int totalCityCount;
            
            public Builder globalCircuitBreakerOpen(boolean globalCircuitBreakerOpen) {
                this.globalCircuitBreakerOpen = globalCircuitBreakerOpen;
                return this;
            }
            
            public Builder globalExceptionCount(int globalExceptionCount) {
                this.globalExceptionCount = globalExceptionCount;
                return this;
            }
            
            public Builder cityCircuitBreakerCount(int cityCircuitBreakerCount) {
                this.cityCircuitBreakerCount = cityCircuitBreakerCount;
                return this;
            }
            
            public Builder totalCityCount(int totalCityCount) {
                this.totalCityCount = totalCityCount;
                return this;
            }
            
            public FallbackStatistics build() {
                return new FallbackStatistics(globalCircuitBreakerOpen, globalExceptionCount,
                                            cityCircuitBreakerCount, totalCityCount);
            }
        }
    }
}
