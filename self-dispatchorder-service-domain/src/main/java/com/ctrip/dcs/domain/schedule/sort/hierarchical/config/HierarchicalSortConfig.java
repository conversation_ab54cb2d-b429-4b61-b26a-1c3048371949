package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 分层排序配置类
 * 包含分层排序系统的所有配置信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class HierarchicalSortConfig {
    
    /**
     * 配置版本号
     */
    private String version;
    
    /**
     * 配置名称
     */
    private String configName;
    
    /**
     * 是否启用分层排序
     */
    private Boolean enabled;
    
    /**
     * 默认排序偏好
     */
    private SortPreference defaultPreference;
    
    /**
     * 大类配置列表
     */
    private List<CategoryConfig> categoryConfigs;
    
    /**
     * 城市灰度配置
     */
    private CityGrayConfig cityGrayConfig;
    
    /**
     * 监控配置
     */
    private MonitorConfig monitorConfig;
    
    /**
     * 降级配置
     */
    private FallbackConfig fallbackConfig;
    
    /**
     * 自定义权重配置（用于CUSTOM偏好）
     */
    private Map<SortCategory, Double> customWeights;
    
    /**
     * 全局超时时间（毫秒）
     */
    private Long globalTimeoutMs;
    
    /**
     * 最大并发数
     */
    private Integer maxConcurrency;
    
    /**
     * 城市灰度配置内部类
     */
    @Data
    @Builder
    public static class CityGrayConfig {
        /**
         * 启用分层排序的城市ID集合
         */
        private Set<Integer> enabledCities;
        
        /**
         * 灰度比例（0-100，表示百分比）
         */
        private Integer grayPercentage;
        
        /**
         * 是否启用城市灰度
         */
        private Boolean enabled;
        
        /**
         * 白名单司机ID集合（强制使用新排序）
         */
        private Set<Long> whitelistDrivers;
        
        /**
         * 黑名单司机ID集合（强制使用旧排序）
         */
        private Set<Long> blacklistDrivers;
    }
    
    /**
     * 监控配置内部类
     */
    @Data
    @Builder
    public static class MonitorConfig {
        /**
         * 是否启用监控
         */
        private Boolean enabled;
        
        /**
         * 是否记录详细日志
         */
        private Boolean detailedLogging;
        
        /**
         * 是否启用性能监控
         */
        private Boolean performanceMonitoring;
        
        /**
         * 是否启用结果对比监控
         */
        private Boolean resultComparison;
        
        /**
         * 监控采样率（0-100，表示百分比）
         */
        private Integer samplingRate;
    }
    
    /**
     * 降级配置内部类
     */
    @Data
    @Builder
    public static class FallbackConfig {
        /**
         * 是否启用降级
         */
        private Boolean enabled;
        
        /**
         * 超时降级阈值（毫秒）
         */
        private Long timeoutThresholdMs;
        
        /**
         * 异常降级阈值（连续异常次数）
         */
        private Integer exceptionThreshold;
        
        /**
         * 降级恢复时间（毫秒）
         */
        private Long recoveryTimeMs;
        
        /**
         * 是否启用熔断器
         */
        private Boolean circuitBreakerEnabled;
        
        /**
         * 熔断器失败率阈值（0-100，表示百分比）
         */
        private Integer failureRateThreshold;
    }
    
    /**
     * 创建默认配置
     * @return 默认的分层排序配置
     */
    public static HierarchicalSortConfig createDefault() {
        return HierarchicalSortConfig.builder()
                .version("1.0.0")
                .configName("default")
                .enabled(true)
                .defaultPreference(SortPreference.BALANCED)
                .globalTimeoutMs(10000L)
                .maxConcurrency(100)
                .cityGrayConfig(CityGrayConfig.builder()
                        .enabled(true)
                        .grayPercentage(10)
                        .build())
                .monitorConfig(MonitorConfig.builder()
                        .enabled(true)
                        .detailedLogging(false)
                        .performanceMonitoring(true)
                        .resultComparison(true)
                        .samplingRate(10)
                        .build())
                .fallbackConfig(FallbackConfig.builder()
                        .enabled(true)
                        .timeoutThresholdMs(5000L)
                        .exceptionThreshold(3)
                        .recoveryTimeMs(60000L)
                        .circuitBreakerEnabled(true)
                        .failureRateThreshold(50)
                        .build())
                .build();
    }
    
    /**
     * 验证配置的有效性
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isValid() {
        if (version == null || version.trim().isEmpty()) {
            return false;
        }
        if (globalTimeoutMs == null || globalTimeoutMs <= 0) {
            return false;
        }
        if (maxConcurrency == null || maxConcurrency <= 0) {
            return false;
        }
        if (categoryConfigs != null) {
            for (CategoryConfig config : categoryConfigs) {
                if (!config.isValid()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 判断是否启用分层排序
     * @return true表示启用，false表示禁用
     */
    public boolean isEffectivelyEnabled() {
        return enabled != null ? enabled : false;
    }
    
    /**
     * 获取有效的默认偏好
     * @return 默认偏好，如果为null则返回BALANCED
     */
    public SortPreference getEffectiveDefaultPreference() {
        return defaultPreference != null ? defaultPreference : SortPreference.BALANCED;
    }
}
