package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import lombok.Builder;
import lombok.Data;

/**
 * 子项配置类
 * 定义每个排序子项的详细配置信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class SubItemConfig {
    
    /**
     * 子项ID（对应FeatureItemId）
     */
    private String itemId;
    
    /**
     * 子项中文名称
     */
    private String displayName;
    
    /**
     * 子项英文标识
     */
    private String code;
    
    /**
     * 所属大类
     */
    private SortCategory category;
    
    /**
     * 在大类中的权重（0-1之间）
     */
    private Double weight;
    
    /**
     * 归一化类型
     */
    private NormalizationType normalizationType;
    
    /**
     * 归一化参数1（如min、mean、center等）
     */
    private Double normalizeParam1;
    
    /**
     * 归一化参数2（如max、std、scale等）
     */
    private Double normalizeParam2;
    
    /**
     * 是否启用该子项
     */
    private Boolean enabled;
    
    /**
     * 子项描述
     */
    private String description;
    
    /**
     * 计算超时时间（毫秒）
     */
    private Long timeoutMs;
    
    /**
     * 是否为核心指标（核心指标计算失败会影响整体排序）
     */
    private Boolean isCoreMetric;
    
    /**
     * 默认值（当计算失败时使用）
     */
    private Double defaultValue;
    
    /**
     * 创建默认的子项配置
     * @param itemId 子项ID
     * @param displayName 显示名称
     * @param category 所属大类
     * @param weight 权重
     * @return 子项配置
     */
    public static SubItemConfig createDefault(String itemId, String displayName, 
                                            SortCategory category, Double weight) {
        return SubItemConfig.builder()
                .itemId(itemId)
                .displayName(displayName)
                .code(itemId)
                .category(category)
                .weight(weight)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(100.0)
                .enabled(true)
                .description("")
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build();
    }
    
    /**
     * 验证配置的有效性
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isValid() {
        if (itemId == null || itemId.trim().isEmpty()) {
            return false;
        }
        if (category == null) {
            return false;
        }
        if (weight == null || weight < 0 || weight > 1) {
            return false;
        }
        if (normalizationType == null) {
            return false;
        }
        if (timeoutMs == null || timeoutMs <= 0) {
            return false;
        }
        return true;
    }
    
    /**
     * 执行归一化计算
     * @param rawValue 原始值
     * @return 归一化后的值
     */
    public double normalize(double rawValue) {
        if (normalizationType == null) {
            return rawValue;
        }
        
        double param1 = normalizeParam1 != null ? normalizeParam1 : 0.0;
        double param2 = normalizeParam2 != null ? normalizeParam2 : 1.0;
        
        return normalizationType.normalize(rawValue, param1, param2);
    }
    
    /**
     * 获取有效的权重值
     * @return 权重值，如果为null则返回0.0
     */
    public double getEffectiveWeight() {
        return weight != null ? weight : 0.0;
    }
    
    /**
     * 获取有效的默认值
     * @return 默认值，如果为null则返回0.0
     */
    public double getEffectiveDefaultValue() {
        return defaultValue != null ? defaultValue : 0.0;
    }
    
    /**
     * 判断是否启用
     * @return true表示启用，false表示禁用
     */
    public boolean isEffectivelyEnabled() {
        return enabled != null ? enabled : true;
    }
    
    /**
     * 判断是否为核心指标
     * @return true表示核心指标，false表示非核心指标
     */
    public boolean isEffectivelyCoreMetric() {
        return isCoreMetric != null ? isCoreMetric : false;
    }
}
