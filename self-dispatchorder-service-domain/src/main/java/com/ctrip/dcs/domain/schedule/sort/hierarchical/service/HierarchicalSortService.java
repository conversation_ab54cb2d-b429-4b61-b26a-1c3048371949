package com.ctrip.dcs.domain.schedule.sort.hierarchical.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.HierarchicalSorter;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalConfigManager;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.manager.FallbackManager;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.manager.GrayControlManager;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.HierarchicalSortResult;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分层排序服务
 * 提供分层排序的主要业务逻辑
 * 
 * <AUTHOR>
 */
@Service
public class HierarchicalSortService {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortService.class);
    
    @Autowired
    private HierarchicalSorter hierarchicalSorter;
    
    @Autowired
    private HierarchicalConfigManager configManager;
    
    @Autowired
    private GrayControlManager grayControlManager;
    
    @Autowired
    private FallbackManager fallbackManager;
    
    /**
     * 执行分层排序
     * 
     * @param order 订单信息
     * @param drivers 司机列表
     * @param sortContext 排序上下文
     * @param preference 排序偏好（可选，默认使用配置中的偏好）
     * @param featureMap 特征项映射
     * @return 排序结果列表
     */
    public List<HierarchicalSortResult> sort(DspOrderVO order,
                                           List<DriverVO> drivers,
                                           SortContext sortContext,
                                           SortPreference preference,
                                           Map<String, Feature> featureMap) {
        if (order == null || drivers == null || drivers.isEmpty()) {
            logger.warn("排序参数无效：订单或司机列表为空");
            return Collections.emptyList();
        }
        
        Integer cityId = order.getCityId();
        HierarchicalSortConfig config = configManager.getCityConfig(cityId);
        
        try {
            // 检查是否应该使用分层排序
            if (!shouldUseHierarchicalSort(order, drivers, config)) {
                logger.debug("不使用分层排序，返回空结果");
                return Collections.emptyList();
            }
            
            // 检查是否应该降级
            if (fallbackManager.shouldFallback(cityId, config)) {
                logger.warn("触发降级策略，不使用分层排序");
                return Collections.emptyList();
            }
            
            // 确定排序偏好
            SortPreference effectivePreference = preference != null ? preference : config.getEffectiveDefaultPreference();
            
            // 转换为排序模型
            List<SortModel> sortModels = drivers.stream()
                    .map(driver -> new SortModel(new DspModelVO(order, driver)))
                    .collect(Collectors.toList());
            
            // 执行分层排序（带超时控制）
            List<HierarchicalSortResult> results = executeWithTimeout(
                    sortModels, sortContext, config, effectivePreference, featureMap);
            
            // 记录成功
            fallbackManager.recordSuccess(cityId, config);
            
            logger.info("分层排序完成：订单={}，城市={}，司机数量={}，结果数量={}，偏好={}",
                       order.getDspOrderId(), cityId, drivers.size(), results.size(), effectivePreference);
            
            return results;
            
        } catch (Exception e) {
            logger.error("执行分层排序时发生异常：订单={}，城市={}", order.getDspOrderId(), cityId, e);
            
            // 记录异常
            fallbackManager.recordException(cityId, e, config);
            
            return Collections.emptyList();
        }
    }
    
    /**
     * 判断是否应该使用分层排序
     * 
     * @param order 订单信息
     * @param drivers 司机列表
     * @param config 配置信息
     * @return true表示使用分层排序，false表示不使用
     */
    private boolean shouldUseHierarchicalSort(DspOrderVO order, List<DriverVO> drivers, HierarchicalSortConfig config) {
        boolean shouldUse = grayControlManager.shouldUseHierarchicalSort(order, drivers, config);
        
        String reason = shouldUse ? "通过灰度控制" : "未通过灰度控制";
        grayControlManager.logGrayDecision(order, drivers, shouldUse, reason);
        
        return shouldUse;
    }
    
    /**
     * 带超时控制的排序执行
     * 
     * @param sortModels 排序模型列表
     * @param sortContext 排序上下文
     * @param config 配置信息
     * @param preference 排序偏好
     * @param featureMap 特征项映射
     * @return 排序结果列表
     */
    private List<HierarchicalSortResult> executeWithTimeout(List<SortModel> sortModels,
                                                           SortContext sortContext,
                                                           HierarchicalSortConfig config,
                                                           SortPreference preference,
                                                           Map<String, Feature> featureMap) {
        long timeoutMs = config.getGlobalTimeoutMs();
        
        try {
            CompletableFuture<List<HierarchicalSortResult>> future = CompletableFuture.supplyAsync(() ->
                    hierarchicalSorter.sort(sortModels, sortContext, config, preference, featureMap));
            
            List<HierarchicalSortResult> results = future.get(timeoutMs, TimeUnit.MILLISECONDS);
            
            return results != null ? results : Collections.emptyList();
            
        } catch (java.util.concurrent.TimeoutException e) {
            logger.error("分层排序超时：{}ms", timeoutMs);
            
            // 记录超时事件
            Integer cityId = sortContext.getDspOrder().getCityId();
            fallbackManager.recordTimeout(cityId, timeoutMs, config);
            
            return Collections.emptyList();
            
        } catch (Exception e) {
            logger.error("分层排序执行异常", e);
            throw new RuntimeException("分层排序执行异常", e);
        }
    }
    
    /**
     * 获取排序结果的司机列表
     * 将分层排序结果转换为司机列表，保持排序顺序
     * 
     * @param sortResults 排序结果列表
     * @param originalDrivers 原始司机列表
     * @return 排序后的司机列表
     */
    public List<DriverVO> extractSortedDrivers(List<HierarchicalSortResult> sortResults, 
                                              List<DriverVO> originalDrivers) {
        if (sortResults == null || sortResults.isEmpty()) {
            return originalDrivers != null ? originalDrivers : Collections.emptyList();
        }
        
        // 创建司机ID到司机对象的映射
        Map<Long, DriverVO> driverMap = originalDrivers.stream()
                .collect(Collectors.toMap(DriverVO::getDriverId, driver -> driver));
        
        // 按排序结果顺序提取司机
        return sortResults.stream()
                .filter(result -> result.getDriverId() != null)
                .map(result -> driverMap.get(result.getDriverId()))
                .filter(driver -> driver != null)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取系统状态信息
     * 
     * @return 系统状态信息
     */
    public SystemStatus getSystemStatus() {
        try {
            HierarchicalSortConfig defaultConfig = configManager.getDefaultConfig();
            GrayControlManager.GrayStatistics grayStats = grayControlManager.getGrayStatistics(defaultConfig);
            FallbackManager.FallbackStatistics fallbackStats = fallbackManager.getFallbackStatistics();
            
            return SystemStatus.builder()
                    .hierarchicalSortEnabled(defaultConfig.isEffectivelyEnabled())
                    .configVersion(defaultConfig.getVersion())
                    .grayStatistics(grayStats)
                    .fallbackStatistics(fallbackStats)
                    .build();
                    
        } catch (Exception e) {
            logger.error("获取系统状态时发生异常", e);
            return SystemStatus.createError("获取状态异常：" + e.getMessage());
        }
    }
    
    /**
     * 系统状态信息内部类
     */
    public static class SystemStatus {
        private final boolean hierarchicalSortEnabled;
        private final String configVersion;
        private final GrayControlManager.GrayStatistics grayStatistics;
        private final FallbackManager.FallbackStatistics fallbackStatistics;
        private final String errorMessage;
        
        private SystemStatus(boolean hierarchicalSortEnabled, String configVersion,
                           GrayControlManager.GrayStatistics grayStatistics,
                           FallbackManager.FallbackStatistics fallbackStatistics,
                           String errorMessage) {
            this.hierarchicalSortEnabled = hierarchicalSortEnabled;
            this.configVersion = configVersion;
            this.grayStatistics = grayStatistics;
            this.fallbackStatistics = fallbackStatistics;
            this.errorMessage = errorMessage;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static SystemStatus createError(String errorMessage) {
            return new SystemStatus(false, null, null, null, errorMessage);
        }
        
        // Getters
        public boolean isHierarchicalSortEnabled() { return hierarchicalSortEnabled; }
        public String getConfigVersion() { return configVersion; }
        public GrayControlManager.GrayStatistics getGrayStatistics() { return grayStatistics; }
        public FallbackManager.FallbackStatistics getFallbackStatistics() { return fallbackStatistics; }
        public String getErrorMessage() { return errorMessage; }
        
        public static class Builder {
            private boolean hierarchicalSortEnabled;
            private String configVersion;
            private GrayControlManager.GrayStatistics grayStatistics;
            private FallbackManager.FallbackStatistics fallbackStatistics;
            
            public Builder hierarchicalSortEnabled(boolean hierarchicalSortEnabled) {
                this.hierarchicalSortEnabled = hierarchicalSortEnabled;
                return this;
            }
            
            public Builder configVersion(String configVersion) {
                this.configVersion = configVersion;
                return this;
            }
            
            public Builder grayStatistics(GrayControlManager.GrayStatistics grayStatistics) {
                this.grayStatistics = grayStatistics;
                return this;
            }
            
            public Builder fallbackStatistics(FallbackManager.FallbackStatistics fallbackStatistics) {
                this.fallbackStatistics = fallbackStatistics;
                return this;
            }
            
            public SystemStatus build() {
                return new SystemStatus(hierarchicalSortEnabled, configVersion, 
                                      grayStatistics, fallbackStatistics, null);
            }
        }
    }
}
