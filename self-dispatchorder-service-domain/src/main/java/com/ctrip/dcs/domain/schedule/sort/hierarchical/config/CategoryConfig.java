package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大类配置类
 * 定义每个排序大类的详细配置信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class CategoryConfig {
    
    /**
     * 大类类型
     */
    private SortCategory category;
    
    /**
     * 大类权重（0-1之间）
     */
    private Double weight;
    
    /**
     * 等级阈值配置
     * Key: 等级枚举
     * Value: 分数阈值（下限）
     */
    private Map<SortGrade, Double> gradeThresholds;
    
    /**
     * 该大类包含的子项配置列表
     */
    private List<SubItemConfig> subItems;
    
    /**
     * 是否启用该大类
     */
    private Boolean enabled;
    
    /**
     * 大类描述
     */
    private String description;
    
    /**
     * 计算超时时间（毫秒）
     */
    private Long timeoutMs;
    
    /**
     * 是否为核心大类（核心大类计算失败会影响整体排序）
     */
    private Boolean isCoreCategory;
    
    /**
     * 默认等级（当计算失败时使用）
     */
    private SortGrade defaultGrade;
    
    /**
     * 创建默认的大类配置
     * @param category 大类类型
     * @param weight 权重
     * @return 大类配置
     */
    public static CategoryConfig createDefault(SortCategory category, Double weight) {
        Map<SortGrade, Double> defaultThresholds = new HashMap<>();
        defaultThresholds.put(SortGrade.A, 85.0);
        defaultThresholds.put(SortGrade.B, 70.0);
        defaultThresholds.put(SortGrade.C, 50.0);
        defaultThresholds.put(SortGrade.D, 0.0);
        
        return CategoryConfig.builder()
                .category(category)
                .weight(weight)
                .gradeThresholds(defaultThresholds)
                .enabled(true)
                .description(category.getDisplayName())
                .timeoutMs(5000L)
                .isCoreCategory(true)
                .defaultGrade(SortGrade.C)
                .build();
    }
    
    /**
     * 验证配置的有效性
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isValid() {
        if (category == null) {
            return false;
        }
        if (weight == null || weight < 0 || weight > 1) {
            return false;
        }
        if (gradeThresholds == null || gradeThresholds.isEmpty()) {
            return false;
        }
        if (timeoutMs == null || timeoutMs <= 0) {
            return false;
        }
        
        // 验证等级阈值的合理性
        for (SortGrade grade : SortGrade.values()) {
            Double threshold = gradeThresholds.get(grade);
            if (threshold == null || threshold < 0 || threshold > 100) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 根据分数获取对应的等级
     * @param score 分数（0-100）
     * @return 对应的等级
     */
    public SortGrade getGradeByScore(double score) {
        if (gradeThresholds == null || gradeThresholds.isEmpty()) {
            return getEffectiveDefaultGrade();
        }
        
        // 按照等级从高到低检查
        for (SortGrade grade : new SortGrade[]{SortGrade.A, SortGrade.B, SortGrade.C, SortGrade.D}) {
            Double threshold = gradeThresholds.get(grade);
            if (threshold != null && score >= threshold) {
                return grade;
            }
        }
        
        return getEffectiveDefaultGrade();
    }
    
    /**
     * 计算该大类的加权平均分数
     * @param subItemScores 子项分数Map（Key: 子项ID, Value: 归一化后的分数）
     * @return 大类的加权平均分数（0-100）
     */
    public double calculateCategoryScore(Map<String, Double> subItemScores) {
        if (subItems == null || subItems.isEmpty() || subItemScores == null || subItemScores.isEmpty()) {
            return 0.0;
        }
        
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;
        
        for (SubItemConfig subItem : subItems) {
            if (!subItem.isEffectivelyEnabled()) {
                continue;
            }
            
            Double score = subItemScores.get(subItem.getItemId());
            if (score != null) {
                double weight = subItem.getEffectiveWeight();
                totalWeightedScore += score * weight * 100; // 转换为0-100分制
                totalWeight += weight;
            }
        }
        
        if (totalWeight == 0.0) {
            return 0.0;
        }
        
        return totalWeightedScore / totalWeight;
    }
    
    /**
     * 获取有效的权重值
     * @return 权重值，如果为null则返回0.0
     */
    public double getEffectiveWeight() {
        return weight != null ? weight : 0.0;
    }
    
    /**
     * 获取有效的默认等级
     * @return 默认等级，如果为null则返回C级
     */
    public SortGrade getEffectiveDefaultGrade() {
        return defaultGrade != null ? defaultGrade : SortGrade.C;
    }
    
    /**
     * 判断是否启用
     * @return true表示启用，false表示禁用
     */
    public boolean isEffectivelyEnabled() {
        return enabled != null ? enabled : true;
    }
    
    /**
     * 判断是否为核心大类
     * @return true表示核心大类，false表示非核心大类
     */
    public boolean isEffectivelyCoreCategory() {
        return isCoreCategory != null ? isCoreCategory : true;
    }
}
