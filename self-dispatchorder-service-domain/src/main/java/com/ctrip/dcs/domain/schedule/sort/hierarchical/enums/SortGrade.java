package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 排序等级枚举
 * 定义A、B、C、D四个等级，用于对司机进行分级评价
 * 
 * <AUTHOR>
 */
public enum SortGrade {
    
    /**
     * A级：优秀（85-100分）
     */
    A("优秀", 4, 85.0, 100.0),
    
    /**
     * B级：良好（70-84分）
     */
    B("良好", 3, 70.0, 84.9),
    
    /**
     * C级：一般（50-69分）
     */
    C("一般", 2, 50.0, 69.9),
    
    /**
     * D级：较差（0-49分）
     */
    D("较差", 1, 0.0, 49.9);
    
    /**
     * 等级中文名称
     */
    private final String displayName;
    
    /**
     * 等级对应的GPA分数（用于加权计算）
     */
    private final int gpaScore;
    
    /**
     * 等级分数下限（包含）
     */
    private final double minScore;
    
    /**
     * 等级分数上限（包含）
     */
    private final double maxScore;
    
    SortGrade(String displayName, int gpaScore, double minScore, double maxScore) {
        this.displayName = displayName;
        this.gpaScore = gpaScore;
        this.minScore = minScore;
        this.maxScore = maxScore;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getGpaScore() {
        return gpaScore;
    }
    
    public double getMinScore() {
        return minScore;
    }
    
    public double getMaxScore() {
        return maxScore;
    }
    
    /**
     * 根据分数获取对应的等级
     * @param score 分数（0-100）
     * @return 对应的等级
     */
    public static SortGrade fromScore(double score) {
        for (SortGrade grade : values()) {
            if (score >= grade.getMinScore() && score <= grade.getMaxScore()) {
                return grade;
            }
        }
        // 默认返回D级
        return D;
    }
    
    /**
     * 判断是否为高等级（A或B级）
     * @return true表示高等级，false表示低等级
     */
    public boolean isHighGrade() {
        return this == A || this == B;
    }
    
    /**
     * 判断是否为低等级（C或D级）
     * @return true表示低等级，false表示高等级
     */
    public boolean isLowGrade() {
        return this == C || this == D;
    }
}
