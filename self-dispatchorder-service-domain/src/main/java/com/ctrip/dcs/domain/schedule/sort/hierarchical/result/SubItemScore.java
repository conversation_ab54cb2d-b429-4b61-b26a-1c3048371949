package com.ctrip.dcs.domain.schedule.sort.hierarchical.result;

import lombok.Builder;
import lombok.Data;

/**
 * 子项分数结果类
 * 记录单个排序子项的计算结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class SubItemScore {
    
    /**
     * 子项ID
     */
    private String itemId;
    
    /**
     * 子项名称
     */
    private String itemName;
    
    /**
     * 原始分数（未归一化）
     */
    private Double rawScore;
    
    /**
     * 归一化后的分数（0-1区间）
     */
    private Double normalizedScore;
    
    /**
     * 在大类中的权重
     */
    private Double weight;
    
    /**
     * 加权分数（归一化分数 * 权重）
     */
    private Double weightedScore;
    
    /**
     * 计算耗时（毫秒）
     */
    private Long calculationTimeMs;
    
    /**
     * 是否计算成功
     */
    private Boolean success;
    
    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;
    
    /**
     * 详细计算信息（用于调试）
     */
    private String detailInfo;
    
    /**
     * 计算时间戳
     */
    private Long timestamp;
    
    /**
     * 创建成功的子项分数结果
     * @param itemId 子项ID
     * @param itemName 子项名称
     * @param rawScore 原始分数
     * @param normalizedScore 归一化分数
     * @param weight 权重
     * @param calculationTimeMs 计算耗时
     * @return 子项分数结果
     */
    public static SubItemScore createSuccess(String itemId, String itemName, 
                                           Double rawScore, Double normalizedScore, 
                                           Double weight, Long calculationTimeMs) {
        double weightedScore = normalizedScore * weight;
        
        return SubItemScore.builder()
                .itemId(itemId)
                .itemName(itemName)
                .rawScore(rawScore)
                .normalizedScore(normalizedScore)
                .weight(weight)
                .weightedScore(weightedScore)
                .calculationTimeMs(calculationTimeMs)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败的子项分数结果
     * @param itemId 子项ID
     * @param itemName 子项名称
     * @param errorMessage 错误信息
     * @param calculationTimeMs 计算耗时
     * @return 子项分数结果
     */
    public static SubItemScore createFailure(String itemId, String itemName, 
                                           String errorMessage, Long calculationTimeMs) {
        return SubItemScore.builder()
                .itemId(itemId)
                .itemName(itemName)
                .rawScore(0.0)
                .normalizedScore(0.0)
                .weight(0.0)
                .weightedScore(0.0)
                .calculationTimeMs(calculationTimeMs)
                .success(false)
                .errorMessage(errorMessage)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取有效的原始分数
     * @return 原始分数，如果为null则返回0.0
     */
    public double getEffectiveRawScore() {
        return rawScore != null ? rawScore : 0.0;
    }
    
    /**
     * 获取有效的归一化分数
     * @return 归一化分数，如果为null则返回0.0
     */
    public double getEffectiveNormalizedScore() {
        return normalizedScore != null ? normalizedScore : 0.0;
    }
    
    /**
     * 获取有效的权重
     * @return 权重，如果为null则返回0.0
     */
    public double getEffectiveWeight() {
        return weight != null ? weight : 0.0;
    }
    
    /**
     * 获取有效的加权分数
     * @return 加权分数，如果为null则返回0.0
     */
    public double getEffectiveWeightedScore() {
        return weightedScore != null ? weightedScore : 0.0;
    }
    
    /**
     * 判断是否计算成功
     * @return true表示成功，false表示失败
     */
    public boolean isEffectivelySuccess() {
        return success != null ? success : false;
    }
    
    /**
     * 获取计算耗时
     * @return 计算耗时，如果为null则返回0
     */
    public long getEffectiveCalculationTimeMs() {
        return calculationTimeMs != null ? calculationTimeMs : 0L;
    }
}
