package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 排序偏好枚举
 * 定义不同的排序策略偏好，每种偏好对应不同的大类权重配置
 * 
 * <AUTHOR>
 */
public enum SortPreference {
    
    /**
     * 效率优先
     * 重点关注时空效率，适用于高峰期或运力紧张时段
     */
    EFFICIENCY_FIRST("效率优先", "EFFICIENCY_FIRST") {
        @Override
        public Map<SortCategory, Double> getWeights() {
            Map<SortCategory, Double> weights = new HashMap<>();
            weights.put(SortCategory.TIME_SPACE_EFFICIENCY, 0.50);  // 时空效率权重最高
            weights.put(SortCategory.SERVICE_QUALITY, 0.20);        // 服务质量权重较低
            weights.put(SortCategory.ORDER_MATCHING, 0.20);         // 订单匹配度权重较低
            weights.put(SortCategory.GLOBAL_EFFICIENCY, 0.10);      // 全局效率权重最低
            return weights;
        }
    },
    
    /**
     * 服务质量优先
     * 重点关注司机服务质量，适用于对服务要求较高的场景
     */
    SERVICE_QUALITY_FIRST("服务质量优先", "SERVICE_QUALITY_FIRST") {
        @Override
        public Map<SortCategory, Double> getWeights() {
            Map<SortCategory, Double> weights = new HashMap<>();
            weights.put(SortCategory.TIME_SPACE_EFFICIENCY, 0.20);  // 时空效率权重较低
            weights.put(SortCategory.SERVICE_QUALITY, 0.50);        // 服务质量权重最高
            weights.put(SortCategory.ORDER_MATCHING, 0.20);         // 订单匹配度权重较低
            weights.put(SortCategory.GLOBAL_EFFICIENCY, 0.10);      // 全局效率权重最低
            return weights;
        }
    },
    
    /**
     * 司机层级优先
     * 重点关注司机分层和等级，适用于需要优先照顾高等级司机的场景
     */
    DRIVER_LEVEL_FIRST("司机层级优先", "DRIVER_LEVEL_FIRST") {
        @Override
        public Map<SortCategory, Double> getWeights() {
            Map<SortCategory, Double> weights = new HashMap<>();
            weights.put(SortCategory.TIME_SPACE_EFFICIENCY, 0.15);  // 时空效率权重最低
            weights.put(SortCategory.SERVICE_QUALITY, 0.55);        // 服务质量权重最高（包含司机分层）
            weights.put(SortCategory.ORDER_MATCHING, 0.15);         // 订单匹配度权重较低
            weights.put(SortCategory.GLOBAL_EFFICIENCY, 0.15);      // 全局效率权重较低
            return weights;
        }
    },
    
    /**
     * 均衡模式
     * 各个维度权重相对均衡，适用于一般场景
     */
    BALANCED("均衡模式", "BALANCED") {
        @Override
        public Map<SortCategory, Double> getWeights() {
            Map<SortCategory, Double> weights = new HashMap<>();
            weights.put(SortCategory.TIME_SPACE_EFFICIENCY, 0.30);  // 时空效率权重适中
            weights.put(SortCategory.SERVICE_QUALITY, 0.30);        // 服务质量权重适中
            weights.put(SortCategory.ORDER_MATCHING, 0.25);         // 订单匹配度权重适中
            weights.put(SortCategory.GLOBAL_EFFICIENCY, 0.15);      // 全局效率权重较低
            return weights;
        }
    },
    
    /**
     * 自定义模式
     * 允许外部自定义权重配置
     */
    CUSTOM("自定义", "CUSTOM") {
        @Override
        public Map<SortCategory, Double> getWeights() {
            // 自定义模式返回空Map，由外部配置提供权重
            return new HashMap<>();
        }
    };
    
    /**
     * 偏好中文名称
     */
    private final String displayName;
    
    /**
     * 偏好英文标识
     */
    private final String code;
    
    SortPreference(String displayName, String code) {
        this.displayName = displayName;
        this.code = code;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * 获取该偏好对应的大类权重配置
     * @return 大类权重Map
     */
    public abstract Map<SortCategory, Double> getWeights();
    
    /**
     * 根据代码获取偏好
     * @param code 偏好代码
     * @return 对应的偏好枚举，如果不存在则返回BALANCED
     */
    public static SortPreference fromCode(String code) {
        for (SortPreference preference : values()) {
            if (preference.getCode().equals(code)) {
                return preference;
            }
        }
        return BALANCED;
    }
}
