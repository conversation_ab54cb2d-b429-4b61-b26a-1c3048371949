package com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.CategoryConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.CategoryScore;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 等级计算器
 * 负责根据各大类分数计算总体等级和GPA
 * 
 * <AUTHOR>
 */
@Component
public class GradeCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(GradeCalculator.class);
    
    /**
     * 计算总体GPA分数
     * 使用加权平均方式计算各大类等级的GPA分数
     * 
     * @param categoryScores 各大类分数结果
     * @param preference 排序偏好（决定权重）
     * @param customWeights 自定义权重（当偏好为CUSTOM时使用）
     * @return 总体GPA分数（0-4之间）
     */
    public double calculateOverallGpaScore(List<CategoryScore> categoryScores, 
                                         SortPreference preference,
                                         Map<SortCategory, Double> customWeights) {
        if (categoryScores == null || categoryScores.isEmpty()) {
            logger.warn("计算总体GPA分数时，大类分数列表为空");
            return 0.0;
        }
        
        try {
            // 获取权重配置
            Map<SortCategory, Double> weights = getEffectiveWeights(preference, customWeights);
            
            double totalWeightedGpaScore = 0.0;
            double totalWeight = 0.0;
            
            // 计算加权GPA分数
            for (CategoryScore categoryScore : categoryScores) {
                if (!categoryScore.isEffectivelySuccess()) {
                    logger.warn("大类{}计算失败，跳过GPA计算", categoryScore.getCategory());
                    continue;
                }
                
                SortCategory category = categoryScore.getCategory();
                Double weight = weights.get(category);
                if (weight == null || weight <= 0) {
                    logger.warn("大类{}权重配置无效，跳过GPA计算", category);
                    continue;
                }
                
                SortGrade grade = categoryScore.getEffectiveGrade();
                double gpaScore = grade.getGpaScore();
                
                totalWeightedGpaScore += gpaScore * weight;
                totalWeight += weight;
                
                logger.debug("大类{}：等级={}，GPA分数={}，权重={}，加权分数={}", 
                           category, grade, gpaScore, weight, gpaScore * weight);
            }
            
            if (totalWeight == 0.0) {
                logger.warn("总权重为0，无法计算GPA分数");
                return 0.0;
            }
            
            double overallGpaScore = totalWeightedGpaScore / totalWeight;
            logger.info("总体GPA分数计算完成：加权总分={}，总权重={}，最终GPA={}",
                       totalWeightedGpaScore, totalWeight, overallGpaScore);
            
            return overallGpaScore;
            
        } catch (Exception e) {
            logger.error("计算总体GPA分数时发生异常", e);
            return 0.0;
        }
    }
    
    /**
     * 根据GPA分数计算总体等级
     * 
     * @param gpaScore GPA分数（0-4之间）
     * @return 总体等级
     */
    public SortGrade calculateOverallGrade(double gpaScore) {
        try {
            // 将GPA分数转换为百分制分数
            double percentageScore = (gpaScore / 4.0) * 100.0;
            
            // 根据分数获取等级
            SortGrade grade = SortGrade.fromScore(percentageScore);
            
            logger.debug("总体等级计算：GPA分数={}，百分制分数={}，等级={}", 
                        gpaScore, percentageScore, grade);
            
            return grade;
            
        } catch (Exception e) {
            logger.error("计算总体等级时发生异常，GPA分数={}", gpaScore, e);
            return SortGrade.D;
        }
    }
    
    /**
     * 计算最终排序分数
     * 结合GPA分数和细分分数，用于最终排序
     * 
     * @param gpaScore GPA分数
     * @param categoryScores 大类分数列表
     * @param preference 排序偏好
     * @param customWeights 自定义权重
     * @return 最终排序分数
     */
    public double calculateFinalSortScore(double gpaScore, List<CategoryScore> categoryScores,
                                        SortPreference preference, Map<SortCategory, Double> customWeights) {
        try {
            // 基础分数：GPA分数转换为百分制
            double baseScore = (gpaScore / 4.0) * 100.0;
            
            // 细分调整：在同等级内根据实际分数进行微调
            double adjustmentScore = calculateAdjustmentScore(categoryScores, preference, customWeights);
            
            // 最终分数 = 基础分数 + 调整分数（调整分数权重较小，避免跨等级）
            double finalScore = baseScore + adjustmentScore * 0.1;
            
            logger.debug("最终排序分数计算：基础分数={}，调整分数={}，最终分数={}", 
                        baseScore, adjustmentScore, finalScore);
            
            return finalScore;
            
        } catch (Exception e) {
            logger.error("计算最终排序分数时发生异常", e);
            return gpaScore * 25.0; // 简单的备用计算方式
        }
    }
    
    /**
     * 计算调整分数
     * 用于在同等级内进行细分排序
     * 
     * @param categoryScores 大类分数列表
     * @param preference 排序偏好
     * @param customWeights 自定义权重
     * @return 调整分数（0-100之间）
     */
    private double calculateAdjustmentScore(List<CategoryScore> categoryScores,
                                          SortPreference preference, Map<SortCategory, Double> customWeights) {
        if (categoryScores == null || categoryScores.isEmpty()) {
            return 0.0;
        }
        
        Map<SortCategory, Double> weights = getEffectiveWeights(preference, customWeights);
        
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;
        
        for (CategoryScore categoryScore : categoryScores) {
            if (!categoryScore.isEffectivelySuccess()) {
                continue;
            }
            
            SortCategory category = categoryScore.getCategory();
            Double weight = weights.get(category);
            if (weight == null || weight <= 0) {
                continue;
            }
            
            double score = categoryScore.getEffectiveScore();
            totalWeightedScore += score * weight;
            totalWeight += weight;
        }
        
        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
    }
    
    /**
     * 获取有效的权重配置
     * 
     * @param preference 排序偏好
     * @param customWeights 自定义权重
     * @return 权重配置Map
     */
    private Map<SortCategory, Double> getEffectiveWeights(SortPreference preference, 
                                                        Map<SortCategory, Double> customWeights) {
        if (preference == SortPreference.CUSTOM && customWeights != null && !customWeights.isEmpty()) {
            return customWeights;
        }
        
        return preference.getWeights();
    }
    
    /**
     * 验证GPA分数的有效性
     * 
     * @param gpaScore GPA分数
     * @return true表示有效，false表示无效
     */
    public boolean isValidGpaScore(double gpaScore) {
        return gpaScore >= 0.0 && gpaScore <= 4.0;
    }
    
    /**
     * 标准化GPA分数
     * 确保GPA分数在有效范围内
     * 
     * @param gpaScore 原始GPA分数
     * @return 标准化后的GPA分数
     */
    public double normalizeGpaScore(double gpaScore) {
        if (gpaScore < 0.0) {
            return 0.0;
        }
        if (gpaScore > 4.0) {
            return 4.0;
        }
        return gpaScore;
    }
}
