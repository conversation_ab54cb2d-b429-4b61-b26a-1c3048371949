package com.ctrip.dcs.domain.schedule.sort.hierarchical.config;

import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItemId;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.NormalizationType;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 分层排序配置管理器
 * 负责管理和提供分层排序的各种配置
 * 
 * <AUTHOR>
 */
@Component
public class HierarchicalConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalConfigManager.class);
    
    /**
     * 默认配置缓存
     */
    private volatile HierarchicalSortConfig defaultConfig;
    
    /**
     * 城市配置缓存
     */
    private final Map<Integer, HierarchicalSortConfig> cityConfigCache = new HashMap<>();
    
    /**
     * 获取默认配置
     * 
     * @return 默认的分层排序配置
     */
    public HierarchicalSortConfig getDefaultConfig() {
        if (defaultConfig == null) {
            synchronized (this) {
                if (defaultConfig == null) {
                    defaultConfig = createDefaultConfig();
                }
            }
        }
        return defaultConfig;
    }
    
    /**
     * 获取城市特定配置
     * 
     * @param cityId 城市ID
     * @return 城市特定的配置，如果不存在则返回默认配置
     */
    public HierarchicalSortConfig getCityConfig(Integer cityId) {
        if (cityId == null) {
            return getDefaultConfig();
        }
        
        HierarchicalSortConfig cityConfig = cityConfigCache.get(cityId);
        if (cityConfig == null) {
            // 这里可以从配置中心或数据库加载城市特定配置
            // 暂时返回默认配置
            cityConfig = getDefaultConfig();
        }
        
        return cityConfig;
    }
    
    /**
     * 创建默认配置
     * 
     * @return 默认配置
     */
    private HierarchicalSortConfig createDefaultConfig() {
        logger.info("创建默认分层排序配置");
        
        // 创建大类配置
        List<CategoryConfig> categoryConfigs = createDefaultCategoryConfigs();
        
        // 创建城市灰度配置
        HierarchicalSortConfig.CityGrayConfig cityGrayConfig = HierarchicalSortConfig.CityGrayConfig.builder()
                .enabled(true)
                .enabledCities(new HashSet<>(Arrays.asList(1, 2, 289))) // 默认启用的城市
                .grayPercentage(10) // 10%灰度
                .whitelistDrivers(new HashSet<>())
                .blacklistDrivers(new HashSet<>())
                .build();
        
        // 创建监控配置
        HierarchicalSortConfig.MonitorConfig monitorConfig = HierarchicalSortConfig.MonitorConfig.builder()
                .enabled(true)
                .detailedLogging(false)
                .performanceMonitoring(true)
                .resultComparison(true)
                .samplingRate(10)
                .build();
        
        // 创建降级配置
        HierarchicalSortConfig.FallbackConfig fallbackConfig = HierarchicalSortConfig.FallbackConfig.builder()
                .enabled(true)
                .timeoutThresholdMs(5000L)
                .exceptionThreshold(3)
                .recoveryTimeMs(60000L)
                .circuitBreakerEnabled(true)
                .failureRateThreshold(50)
                .build();
        
        return HierarchicalSortConfig.builder()
                .version("1.0.0")
                .configName("default")
                .enabled(true)
                .defaultPreference(SortPreference.BALANCED)
                .categoryConfigs(categoryConfigs)
                .cityGrayConfig(cityGrayConfig)
                .monitorConfig(monitorConfig)
                .fallbackConfig(fallbackConfig)
                .globalTimeoutMs(10000L)
                .maxConcurrency(100)
                .build();
    }
    
    /**
     * 创建默认大类配置
     * 
     * @return 大类配置列表
     */
    private List<CategoryConfig> createDefaultCategoryConfigs() {
        List<CategoryConfig> categoryConfigs = new ArrayList<>();
        
        // 时空效率类配置
        CategoryConfig timeSpaceConfig = CategoryConfig.createDefault(SortCategory.TIME_SPACE_EFFICIENCY, 0.30);
        timeSpaceConfig.setSubItems(createTimeSpaceSubItems());
        categoryConfigs.add(timeSpaceConfig);
        
        // 服务质量类配置
        CategoryConfig serviceQualityConfig = CategoryConfig.createDefault(SortCategory.SERVICE_QUALITY, 0.30);
        serviceQualityConfig.setSubItems(createServiceQualitySubItems());
        categoryConfigs.add(serviceQualityConfig);
        
        // 订单匹配度类配置
        CategoryConfig orderMatchingConfig = CategoryConfig.createDefault(SortCategory.ORDER_MATCHING, 0.25);
        orderMatchingConfig.setSubItems(createOrderMatchingSubItems());
        categoryConfigs.add(orderMatchingConfig);
        
        // 全局效率类配置
        CategoryConfig globalEfficiencyConfig = CategoryConfig.createDefault(SortCategory.GLOBAL_EFFICIENCY, 0.15);
        globalEfficiencyConfig.setSubItems(createGlobalEfficiencySubItems());
        categoryConfigs.add(globalEfficiencyConfig);
        
        return categoryConfigs;
    }
    
    /**
     * 创建时空效率类子项配置
     * 
     * @return 子项配置列表
     */
    private List<SubItemConfig> createTimeSpaceSubItems() {
        List<SubItemConfig> subItems = new ArrayList<>();
        
        // F9: 局部时间间隔 - 时间间隔越小越好，使用逆向归一化
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F9.name())
                .displayName("局部时间间隔")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.25)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(210.0) // 最大时间间隔（分钟）
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F10: 局部空驶距离 - 空驶距离越小越好，使用逆向归一化
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F10.name())
                .displayName("局部空驶距离")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.25)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(-20.0) // 最小值（可能为负）
                .normalizeParam2(0.0)   // 最大值
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F21: 司机空驶时长排序
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F21.name())
                .displayName("司机空驶时长")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.20)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(3600.0) // 最大空驶时长（秒）
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F6: 空驶距离
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F6.name())
                .displayName("空驶距离")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.15)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(50000.0) // 最大空驶距离（米）
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F4: 接驾时间成本
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F4.name())
                .displayName("接驾时间成本")
                .category(SortCategory.TIME_SPACE_EFFICIENCY)
                .weight(0.15)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1800.0) // 最大接驾时间（秒）
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        return subItems;
    }
    
    /**
     * 创建服务质量类子项配置
     * 
     * @return 子项配置列表
     */
    private List<SubItemConfig> createServiceQualitySubItems() {
        List<SubItemConfig> subItems = new ArrayList<>();
        
        // F2: 司机分占比 - 分数越高越好，使用正向归一化
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F2.name())
                .displayName("司机分占比")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.35)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F19: 司机分层新 - 分层值越高越好，无需归一化
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F19.name())
                .displayName("司机分层")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.30)
                .normalizationType(NormalizationType.LOG_NORMALIZE)
                .normalizeParam1(1.0)
                .normalizeParam2(10000.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(1.0)
                .build());
        
        // F1: 司机分
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F1.name())
                .displayName("司机分")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.20)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1000.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F40: 司机分类
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F40.name())
                .displayName("司机分类")
                .category(SortCategory.SERVICE_QUALITY)
                .weight(0.15)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(10.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        return subItems;
    }
    
    /**
     * 创建订单匹配度类子项配置
     * 
     * @return 子项配置列表
     */
    private List<SubItemConfig> createOrderMatchingSubItems() {
        List<SubItemConfig> subItems = new ArrayList<>();
        
        // F14: 里程价值
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F14.name())
                .displayName("里程价值")
                .category(SortCategory.ORDER_MATCHING)
                .weight(0.35)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(10.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F11: 未来接单能力
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F11.name())
                .displayName("未来接单能力")
                .category(SortCategory.ORDER_MATCHING)
                .weight(0.30)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(10.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F41: 司机订单衔接排序
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F41.name())
                .displayName("订单衔接")
                .category(SortCategory.ORDER_MATCHING)
                .weight(0.20)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(100.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F39: 临近单距离耗时优先
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F39.name())
                .displayName("临近单优先")
                .category(SortCategory.ORDER_MATCHING)
                .weight(0.15)
                .normalizationType(NormalizationType.MIN_MAX_NEGATIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1800.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        return subItems;
    }
    
    /**
     * 创建全局效率类子项配置
     * 
     * @return 子项配置列表
     */
    private List<SubItemConfig> createGlobalEfficiencySubItems() {
        List<SubItemConfig> subItems = new ArrayList<>();
        
        // F13: 司机日收益
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F13.name())
                .displayName("司机日收益")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.40)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(true)
                .defaultValue(0.0)
                .build());
        
        // F12: 司机月收益
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F12.name())
                .displayName("司机月收益")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.25)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(1.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F17: 供应商订单分流特征
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F17.name())
                .displayName("订单分流")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.20)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(100.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        // F42: 高峰时段司机
        subItems.add(SubItemConfig.builder()
                .itemId(FeatureItemId.F42.name())
                .displayName("高峰时段")
                .category(SortCategory.GLOBAL_EFFICIENCY)
                .weight(0.15)
                .normalizationType(NormalizationType.MIN_MAX_POSITIVE)
                .normalizeParam1(0.0)
                .normalizeParam2(10.0)
                .enabled(true)
                .timeoutMs(1000L)
                .isCoreMetric(false)
                .defaultValue(0.0)
                .build());
        
        return subItems;
    }
    
    /**
     * 刷新配置缓存
     * 用于配置热更新
     */
    public void refreshConfig() {
        logger.info("刷新分层排序配置缓存");
        synchronized (this) {
            defaultConfig = null;
            cityConfigCache.clear();
        }
    }
    
    /**
     * 验证配置有效性
     * 
     * @param config 配置对象
     * @return true表示有效，false表示无效
     */
    public boolean validateConfig(HierarchicalSortConfig config) {
        if (config == null) {
            return false;
        }
        
        return config.isValid();
    }
}
