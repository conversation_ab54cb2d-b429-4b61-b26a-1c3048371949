package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 排序大类枚举
 * 定义四个主要的排序维度，每个维度包含多个子项
 * 
 * <AUTHOR>
 */
public enum SortCategory {
    
    /**
     * 时空效率类
     * 主要关注司机的空驶时间、空驶距离等效率指标
     */
    TIME_SPACE_EFFICIENCY("时空效率", "TIME_SPACE_EFFICIENCY", 1),
    
    /**
     * 服务质量类  
     * 主要关注司机的服务水平、司机分等质量指标
     */
    SERVICE_QUALITY("服务质量", "SERVICE_QUALITY", 2),
    
    /**
     * 订单匹配度类
     * 主要关注订单与司机的匹配程度、里程价值等
     */
    ORDER_MATCHING("订单匹配度", "ORDER_MATCHING", 3),
    
    /**
     * 全局效率类
     * 主要关注收益均衡、区域调度等全局优化指标
     */
    GLOBAL_EFFICIENCY("全局效率", "GLOBAL_EFFICIENCY", 4);
    
    /**
     * 大类中文名称
     */
    private final String displayName;
    
    /**
     * 大类英文标识
     */
    private final String code;
    
    /**
     * 大类排序优先级（数字越小优先级越高）
     */
    private final int priority;
    
    SortCategory(String displayName, String code, int priority) {
        this.displayName = displayName;
        this.code = code;
        this.priority = priority;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getCode() {
        return code;
    }
    
    public int getPriority() {
        return priority;
    }
    
    /**
     * 根据代码获取大类
     * @param code 大类代码
     * @return 对应的大类枚举，如果不存在则返回null
     */
    public static SortCategory fromCode(String code) {
        for (SortCategory category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }
}
