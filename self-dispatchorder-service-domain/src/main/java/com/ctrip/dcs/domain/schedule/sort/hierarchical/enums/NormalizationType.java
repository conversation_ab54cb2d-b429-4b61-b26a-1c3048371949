package com.ctrip.dcs.domain.schedule.sort.hierarchical.enums;

/**
 * 归一化类型枚举
 * 定义不同的归一化方式，用于将原始分数转换为标准化分数
 * 
 * <AUTHOR>
 */
public enum NormalizationType {
    
    /**
     * Min-Max归一化（正向）
     * 公式：(x - min) / (max - min)
     * 适用于值越大越好的指标
     */
    MIN_MAX_POSITIVE("Min-Max正向归一化", "MIN_MAX_POSITIVE") {
        @Override
        public double normalize(double value, double min, double max) {
            if (Double.compare(max, min) == 0) {
                return 1.0;
            }
            return Math.max(0.0, Math.min(1.0, (value - min) / (max - min)));
        }
    },
    
    /**
     * Min-Max归一化（逆向）
     * 公式：(max - x) / (max - min)
     * 适用于值越小越好的指标
     */
    MIN_MAX_NEGATIVE("Min-Max逆向归一化", "MIN_MAX_NEGATIVE") {
        @Override
        public double normalize(double value, double min, double max) {
            if (Double.compare(max, min) == 0) {
                return 1.0;
            }
            return Math.max(0.0, Math.min(1.0, (max - value) / (max - min)));
        }
    },
    
    /**
     * Z-Score标准化
     * 公式：(x - mean) / std
     * 适用于正态分布的数据
     */
    Z_SCORE("Z-Score标准化", "Z_SCORE") {
        @Override
        public double normalize(double value, double mean, double std) {
            if (Double.compare(std, 0.0) == 0) {
                return 0.0;
            }
            double zScore = (value - mean) / std;
            // 将Z-Score转换为0-1区间，使用sigmoid函数
            return 1.0 / (1.0 + Math.exp(-zScore));
        }
    },
    
    /**
     * 对数归一化
     * 公式：log(x) / log(max)
     * 适用于长尾分布的数据
     */
    LOG_NORMALIZE("对数归一化", "LOG_NORMALIZE") {
        @Override
        public double normalize(double value, double min, double max) {
            if (value <= 0 || max <= 0) {
                return 0.0;
            }
            double logValue = Math.log(value);
            double logMax = Math.log(max);
            if (Double.compare(logMax, 0.0) == 0) {
                return 1.0;
            }
            return Math.max(0.0, Math.min(1.0, logValue / logMax));
        }
    },
    
    /**
     * Sigmoid归一化
     * 公式：1 / (1 + exp(-x))
     * 适用于需要平滑过渡的场景
     */
    SIGMOID("Sigmoid归一化", "SIGMOID") {
        @Override
        public double normalize(double value, double center, double scale) {
            return 1.0 / (1.0 + Math.exp(-(value - center) / scale));
        }
    },
    
    /**
     * 排名归一化
     * 公式：rank / total
     * 适用于基于排名的归一化
     */
    RANK_NORMALIZE("排名归一化", "RANK_NORMALIZE") {
        @Override
        public double normalize(double rank, double total, double unused) {
            if (total <= 0) {
                return 0.0;
            }
            return Math.max(0.0, Math.min(1.0, (total - rank + 1) / total));
        }
    },
    
    /**
     * 无归一化
     * 直接返回原始值
     */
    NONE("无归一化", "NONE") {
        @Override
        public double normalize(double value, double param1, double param2) {
            return value;
        }
    };
    
    /**
     * 归一化类型中文名称
     */
    private final String displayName;
    
    /**
     * 归一化类型英文标识
     */
    private final String code;
    
    NormalizationType(String displayName, String code) {
        this.displayName = displayName;
        this.code = code;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getCode() {
        return code;
    }
    
    /**
     * 执行归一化计算
     * @param value 原始值
     * @param param1 参数1（如min、mean、center等）
     * @param param2 参数2（如max、std、scale等）
     * @return 归一化后的值（通常在0-1区间）
     */
    public abstract double normalize(double value, double param1, double param2);
    
    /**
     * 根据代码获取归一化类型
     * @param code 归一化类型代码
     * @return 对应的归一化类型枚举，如果不存在则返回MIN_MAX_POSITIVE
     */
    public static NormalizationType fromCode(String code) {
        for (NormalizationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return MIN_MAX_POSITIVE;
    }
}
