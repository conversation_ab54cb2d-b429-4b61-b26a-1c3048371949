package com.ctrip.dcs.domain.schedule.sort.hierarchical.result;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 大类分数结果类
 * 记录单个排序大类的计算结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class CategoryScore {
    
    /**
     * 大类类型
     */
    private SortCategory category;
    
    /**
     * 大类名称
     */
    private String categoryName;
    
    /**
     * 大类分数（0-100分制）
     */
    private Double score;
    
    /**
     * 大类等级
     */
    private SortGrade grade;
    
    /**
     * 大类权重
     */
    private Double weight;
    
    /**
     * 加权分数（用于计算总体GPA）
     */
    private Double weightedGpaScore;
    
    /**
     * 子项分数列表
     */
    private List<SubItemScore> subItemScores;
    
    /**
     * 计算耗时（毫秒）
     */
    private Long calculationTimeMs;
    
    /**
     * 是否计算成功
     */
    private Boolean success;
    
    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;
    
    /**
     * 薄弱子项分析
     */
    private List<String> weakSubItems;
    
    /**
     * 改进建议
     */
    private List<String> improvementSuggestions;
    
    /**
     * 计算时间戳
     */
    private Long timestamp;
    
    /**
     * 创建成功的大类分数结果
     * @param category 大类类型
     * @param score 大类分数
     * @param grade 大类等级
     * @param weight 大类权重
     * @param subItemScores 子项分数列表
     * @param calculationTimeMs 计算耗时
     * @return 大类分数结果
     */
    public static CategoryScore createSuccess(SortCategory category, Double score, 
                                            SortGrade grade, Double weight,
                                            List<SubItemScore> subItemScores, 
                                            Long calculationTimeMs) {
        double weightedGpaScore = grade.getGpaScore() * weight;
        
        return CategoryScore.builder()
                .category(category)
                .categoryName(category.getDisplayName())
                .score(score)
                .grade(grade)
                .weight(weight)
                .weightedGpaScore(weightedGpaScore)
                .subItemScores(subItemScores)
                .calculationTimeMs(calculationTimeMs)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败的大类分数结果
     * @param category 大类类型
     * @param errorMessage 错误信息
     * @param calculationTimeMs 计算耗时
     * @return 大类分数结果
     */
    public static CategoryScore createFailure(SortCategory category, String errorMessage, 
                                            Long calculationTimeMs) {
        return CategoryScore.builder()
                .category(category)
                .categoryName(category.getDisplayName())
                .score(0.0)
                .grade(SortGrade.D)
                .weight(0.0)
                .weightedGpaScore(0.0)
                .calculationTimeMs(calculationTimeMs)
                .success(false)
                .errorMessage(errorMessage)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取有效的分数
     * @return 分数，如果为null则返回0.0
     */
    public double getEffectiveScore() {
        return score != null ? score : 0.0;
    }
    
    /**
     * 获取有效的等级
     * @return 等级，如果为null则返回D级
     */
    public SortGrade getEffectiveGrade() {
        return grade != null ? grade : SortGrade.D;
    }
    
    /**
     * 获取有效的权重
     * @return 权重，如果为null则返回0.0
     */
    public double getEffectiveWeight() {
        return weight != null ? weight : 0.0;
    }
    
    /**
     * 获取有效的加权GPA分数
     * @return 加权GPA分数，如果为null则返回0.0
     */
    public double getEffectiveWeightedGpaScore() {
        return weightedGpaScore != null ? weightedGpaScore : 0.0;
    }
    
    /**
     * 判断是否计算成功
     * @return true表示成功，false表示失败
     */
    public boolean isEffectivelySuccess() {
        return success != null ? success : false;
    }
    
    /**
     * 获取计算耗时
     * @return 计算耗时，如果为null则返回0
     */
    public long getEffectiveCalculationTimeMs() {
        return calculationTimeMs != null ? calculationTimeMs : 0L;
    }
    
    /**
     * 判断是否为高等级大类
     * @return true表示高等级（A或B），false表示低等级
     */
    public boolean isHighGrade() {
        return getEffectiveGrade().isHighGrade();
    }
    
    /**
     * 判断是否为低等级大类
     * @return true表示低等级（C或D），false表示高等级
     */
    public boolean isLowGrade() {
        return getEffectiveGrade().isLowGrade();
    }
    
    /**
     * 获取成功的子项数量
     * @return 成功的子项数量
     */
    public int getSuccessfulSubItemCount() {
        if (subItemScores == null) {
            return 0;
        }
        return (int) subItemScores.stream()
                .filter(SubItemScore::isEffectivelySuccess)
                .count();
    }
    
    /**
     * 获取失败的子项数量
     * @return 失败的子项数量
     */
    public int getFailedSubItemCount() {
        if (subItemScores == null) {
            return 0;
        }
        return (int) subItemScores.stream()
                .filter(item -> !item.isEffectivelySuccess())
                .count();
    }
}
