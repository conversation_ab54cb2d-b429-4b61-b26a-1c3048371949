package com.ctrip.dcs.domain.schedule.sort.hierarchical.result;

import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 分层排序结果类
 * 记录单个司机的完整排序结果
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class HierarchicalSortResult {
    
    /**
     * 司机ID
     */
    private Long driverId;
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 总体等级
     */
    private SortGrade overallGrade;
    
    /**
     * 总体GPA分数（加权平均）
     */
    private Double overallGpaScore;
    
    /**
     * 最终排序分数（用于排序比较）
     */
    private Double finalSortScore;
    
    /**
     * 排序偏好
     */
    private SortPreference preference;
    
    /**
     * 各大类分数结果
     */
    private List<CategoryScore> categoryScores;
    
    /**
     * 总计算耗时（毫秒）
     */
    private Long totalCalculationTimeMs;
    
    /**
     * 是否计算成功
     */
    private Boolean success;
    
    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;
    
    /**
     * 排序排名（在所有司机中的排名）
     */
    private Integer rank;
    
    /**
     * 薄弱项分析（需要改进的方面）
     */
    private List<String> weaknesses;
    
    /**
     * 优势项分析（表现优秀的方面）
     */
    private List<String> strengths;
    
    /**
     * 改进建议
     */
    private List<String> improvementSuggestions;
    
    /**
     * 调试信息
     */
    private Map<String, Object> debugInfo;
    
    /**
     * 计算时间戳
     */
    private Long timestamp;
    
    /**
     * 配置版本号
     */
    private String configVersion;
    
    /**
     * 创建成功的排序结果
     * @param driverId 司机ID
     * @param orderId 订单ID
     * @param overallGrade 总体等级
     * @param overallGpaScore 总体GPA分数
     * @param finalSortScore 最终排序分数
     * @param preference 排序偏好
     * @param categoryScores 大类分数列表
     * @param totalCalculationTimeMs 总计算耗时
     * @return 排序结果
     */
    public static HierarchicalSortResult createSuccess(Long driverId, String orderId,
                                                     SortGrade overallGrade, Double overallGpaScore,
                                                     Double finalSortScore, SortPreference preference,
                                                     List<CategoryScore> categoryScores,
                                                     Long totalCalculationTimeMs) {
        return HierarchicalSortResult.builder()
                .driverId(driverId)
                .orderId(orderId)
                .overallGrade(overallGrade)
                .overallGpaScore(overallGpaScore)
                .finalSortScore(finalSortScore)
                .preference(preference)
                .categoryScores(categoryScores)
                .totalCalculationTimeMs(totalCalculationTimeMs)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败的排序结果
     * @param driverId 司机ID
     * @param orderId 订单ID
     * @param errorMessage 错误信息
     * @param totalCalculationTimeMs 总计算耗时
     * @return 排序结果
     */
    public static HierarchicalSortResult createFailure(Long driverId, String orderId,
                                                      String errorMessage, Long totalCalculationTimeMs) {
        return HierarchicalSortResult.builder()
                .driverId(driverId)
                .orderId(orderId)
                .overallGrade(SortGrade.D)
                .overallGpaScore(0.0)
                .finalSortScore(0.0)
                .totalCalculationTimeMs(totalCalculationTimeMs)
                .success(false)
                .errorMessage(errorMessage)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 获取有效的总体等级
     * @return 总体等级，如果为null则返回D级
     */
    public SortGrade getEffectiveOverallGrade() {
        return overallGrade != null ? overallGrade : SortGrade.D;
    }
    
    /**
     * 获取有效的总体GPA分数
     * @return 总体GPA分数，如果为null则返回0.0
     */
    public double getEffectiveOverallGpaScore() {
        return overallGpaScore != null ? overallGpaScore : 0.0;
    }
    
    /**
     * 获取有效的最终排序分数
     * @return 最终排序分数，如果为null则返回0.0
     */
    public double getEffectiveFinalSortScore() {
        return finalSortScore != null ? finalSortScore : 0.0;
    }
    
    /**
     * 判断是否计算成功
     * @return true表示成功，false表示失败
     */
    public boolean isEffectivelySuccess() {
        return success != null ? success : false;
    }
    
    /**
     * 获取总计算耗时
     * @return 总计算耗时，如果为null则返回0
     */
    public long getEffectiveTotalCalculationTimeMs() {
        return totalCalculationTimeMs != null ? totalCalculationTimeMs : 0L;
    }
    
    /**
     * 获取排名
     * @return 排名，如果为null则返回Integer.MAX_VALUE
     */
    public int getEffectiveRank() {
        return rank != null ? rank : Integer.MAX_VALUE;
    }
    
    /**
     * 判断是否为高等级司机
     * @return true表示高等级（A或B），false表示低等级
     */
    public boolean isHighGradeDriver() {
        return getEffectiveOverallGrade().isHighGrade();
    }
    
    /**
     * 判断是否为低等级司机
     * @return true表示低等级（C或D），false表示高等级
     */
    public boolean isLowGradeDriver() {
        return getEffectiveOverallGrade().isLowGrade();
    }
    
    /**
     * 获取成功的大类数量
     * @return 成功的大类数量
     */
    public int getSuccessfulCategoryCount() {
        if (categoryScores == null) {
            return 0;
        }
        return (int) categoryScores.stream()
                .filter(CategoryScore::isEffectivelySuccess)
                .count();
    }
    
    /**
     * 获取失败的大类数量
     * @return 失败的大类数量
     */
    public int getFailedCategoryCount() {
        if (categoryScores == null) {
            return 0;
        }
        return (int) categoryScores.stream()
                .filter(category -> !category.isEffectivelySuccess())
                .count();
    }
}
