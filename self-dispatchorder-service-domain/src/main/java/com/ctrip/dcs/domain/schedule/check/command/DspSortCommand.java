package com.ctrip.dcs.domain.schedule.check.command;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class DspSortCommand {

    private DspOrderVO dspOrder;

    private SubSkuVO subSku;

    private List<DriverVO> drivers;

    public DspSortCommand(DspOrderVO dspOrder, SubSkuVO subSku, List<DriverVO> drivers) {
        this.dspOrder = dspOrder;
        this.subSku = subSku;
        this.drivers = drivers;
    }
}
