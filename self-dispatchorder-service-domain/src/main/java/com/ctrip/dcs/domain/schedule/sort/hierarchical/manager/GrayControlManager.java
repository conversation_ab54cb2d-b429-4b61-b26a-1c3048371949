package com.ctrip.dcs.domain.schedule.sort.hierarchical.manager;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 灰度控制管理器
 * 负责控制分层排序功能的灰度发布
 * 
 * <AUTHOR>
 */
@Component
public class GrayControlManager {
    
    private static final Logger logger = LoggerFactory.getLogger(GrayControlManager.class);
    
    /**
     * 判断是否应该使用分层排序
     * 
     * @param order 订单信息
     * @param drivers 司机列表
     * @param config 配置信息
     * @return true表示使用分层排序，false表示使用原排序
     */
    public boolean shouldUseHierarchicalSort(DspOrderVO order, 
                                           java.util.List<DriverVO> drivers,
                                           HierarchicalSortConfig config) {
        if (config == null || !config.isEffectivelyEnabled()) {
            logger.debug("分层排序功能未启用");
            return false;
        }
        
        HierarchicalSortConfig.CityGrayConfig grayConfig = config.getCityGrayConfig();
        if (grayConfig == null || !grayConfig.getEnabled()) {
            logger.debug("城市灰度控制未启用");
            return false;
        }
        
        try {
            // 检查城市是否在启用列表中
            if (!isCityEnabled(order.getCityId(), grayConfig)) {
                logger.debug("城市{}未在分层排序启用列表中", order.getCityId());
                return false;
            }
            
            // 检查司机黑白名单
            if (hasBlacklistDrivers(drivers, grayConfig)) {
                logger.debug("存在黑名单司机，使用原排序");
                return false;
            }
            
            if (hasWhitelistDrivers(drivers, grayConfig)) {
                logger.debug("存在白名单司机，强制使用分层排序");
                return true;
            }
            
            // 根据灰度比例决定
            return shouldUseByGrayPercentage(order, grayConfig);
            
        } catch (Exception e) {
            logger.error("判断是否使用分层排序时发生异常", e);
            return false; // 异常时默认使用原排序
        }
    }
    
    /**
     * 检查城市是否启用分层排序
     * 
     * @param cityId 城市ID
     * @param grayConfig 灰度配置
     * @return true表示启用，false表示未启用
     */
    private boolean isCityEnabled(Integer cityId, HierarchicalSortConfig.CityGrayConfig grayConfig) {
        if (cityId == null) {
            return false;
        }
        
        Set<Integer> enabledCities = grayConfig.getEnabledCities();
        if (enabledCities == null || enabledCities.isEmpty()) {
            return false;
        }
        
        return enabledCities.contains(cityId);
    }
    
    /**
     * 检查是否有黑名单司机
     * 
     * @param drivers 司机列表
     * @param grayConfig 灰度配置
     * @return true表示有黑名单司机，false表示没有
     */
    private boolean hasBlacklistDrivers(java.util.List<DriverVO> drivers, 
                                      HierarchicalSortConfig.CityGrayConfig grayConfig) {
        Set<Long> blacklistDrivers = grayConfig.getBlacklistDrivers();
        if (blacklistDrivers == null || blacklistDrivers.isEmpty()) {
            return false;
        }
        
        return drivers.stream()
                .anyMatch(driver -> blacklistDrivers.contains(driver.getDriverId()));
    }
    
    /**
     * 检查是否有白名单司机
     * 
     * @param drivers 司机列表
     * @param grayConfig 灰度配置
     * @return true表示有白名单司机，false表示没有
     */
    private boolean hasWhitelistDrivers(java.util.List<DriverVO> drivers,
                                      HierarchicalSortConfig.CityGrayConfig grayConfig) {
        Set<Long> whitelistDrivers = grayConfig.getWhitelistDrivers();
        if (whitelistDrivers == null || whitelistDrivers.isEmpty()) {
            return false;
        }
        
        return drivers.stream()
                .anyMatch(driver -> whitelistDrivers.contains(driver.getDriverId()));
    }
    
    /**
     * 根据灰度比例决定是否使用分层排序
     * 
     * @param order 订单信息
     * @param grayConfig 灰度配置
     * @return true表示使用分层排序，false表示使用原排序
     */
    private boolean shouldUseByGrayPercentage(DspOrderVO order, 
                                            HierarchicalSortConfig.CityGrayConfig grayConfig) {
        Integer grayPercentage = grayConfig.getGrayPercentage();
        if (grayPercentage == null || grayPercentage <= 0) {
            return false;
        }
        
        if (grayPercentage >= 100) {
            return true;
        }
        
        // 使用订单ID的哈希值来决定是否进入灰度
        String orderId = order.getDspOrderId();
        if (orderId == null || orderId.isEmpty()) {
            return false;
        }
        
        int hash = Math.abs(orderId.hashCode());
        int remainder = hash % 100;
        
        boolean shouldUse = remainder < grayPercentage;
        
        logger.debug("订单{}灰度判断：hash={}，remainder={}，灰度比例={}%，结果={}",
                    orderId, hash, remainder, grayPercentage, shouldUse);
        
        return shouldUse;
    }
    
    /**
     * 记录灰度决策日志
     * 
     * @param order 订单信息
     * @param drivers 司机列表
     * @param decision 决策结果
     * @param reason 决策原因
     */
    public void logGrayDecision(DspOrderVO order, 
                              java.util.List<DriverVO> drivers,
                              boolean decision, 
                              String reason) {
        try {
            logger.info("分层排序灰度决策：订单={}，城市={}，司机数量={}，决策={}，原因={}",
                       order.getDspOrderId(), 
                       order.getCityId(),
                       drivers != null ? drivers.size() : 0,
                       decision ? "使用分层排序" : "使用原排序",
                       reason);
        } catch (Exception e) {
            logger.error("记录灰度决策日志时发生异常", e);
        }
    }
    
    /**
     * 获取灰度统计信息
     * 
     * @param config 配置信息
     * @return 灰度统计信息
     */
    public GrayStatistics getGrayStatistics(HierarchicalSortConfig config) {
        if (config == null || config.getCityGrayConfig() == null) {
            return GrayStatistics.empty();
        }
        
        HierarchicalSortConfig.CityGrayConfig grayConfig = config.getCityGrayConfig();
        
        return GrayStatistics.builder()
                .enabled(grayConfig.getEnabled())
                .enabledCityCount(grayConfig.getEnabledCities() != null ? grayConfig.getEnabledCities().size() : 0)
                .grayPercentage(grayConfig.getGrayPercentage())
                .whitelistDriverCount(grayConfig.getWhitelistDrivers() != null ? grayConfig.getWhitelistDrivers().size() : 0)
                .blacklistDriverCount(grayConfig.getBlacklistDrivers() != null ? grayConfig.getBlacklistDrivers().size() : 0)
                .build();
    }
    
    /**
     * 灰度统计信息内部类
     */
    public static class GrayStatistics {
        private final boolean enabled;
        private final int enabledCityCount;
        private final Integer grayPercentage;
        private final int whitelistDriverCount;
        private final int blacklistDriverCount;
        
        private GrayStatistics(boolean enabled, int enabledCityCount, Integer grayPercentage,
                             int whitelistDriverCount, int blacklistDriverCount) {
            this.enabled = enabled;
            this.enabledCityCount = enabledCityCount;
            this.grayPercentage = grayPercentage;
            this.whitelistDriverCount = whitelistDriverCount;
            this.blacklistDriverCount = blacklistDriverCount;
        }
        
        public static GrayStatistics builder() {
            return new Builder();
        }
        
        public static GrayStatistics empty() {
            return new GrayStatistics(false, 0, 0, 0, 0);
        }
        
        // Getters
        public boolean isEnabled() { return enabled; }
        public int getEnabledCityCount() { return enabledCityCount; }
        public Integer getGrayPercentage() { return grayPercentage; }
        public int getWhitelistDriverCount() { return whitelistDriverCount; }
        public int getBlacklistDriverCount() { return blacklistDriverCount; }
        
        public static class Builder {
            private boolean enabled;
            private int enabledCityCount;
            private Integer grayPercentage;
            private int whitelistDriverCount;
            private int blacklistDriverCount;
            
            public Builder enabled(Boolean enabled) {
                this.enabled = enabled != null ? enabled : false;
                return this;
            }
            
            public Builder enabledCityCount(int enabledCityCount) {
                this.enabledCityCount = enabledCityCount;
                return this;
            }
            
            public Builder grayPercentage(Integer grayPercentage) {
                this.grayPercentage = grayPercentage;
                return this;
            }
            
            public Builder whitelistDriverCount(int whitelistDriverCount) {
                this.whitelistDriverCount = whitelistDriverCount;
                return this;
            }
            
            public Builder blacklistDriverCount(int blacklistDriverCount) {
                this.blacklistDriverCount = blacklistDriverCount;
                return this;
            }
            
            public GrayStatistics build() {
                return new GrayStatistics(enabled, enabledCityCount, grayPercentage,
                                        whitelistDriverCount, blacklistDriverCount);
            }
        }
    }
}
