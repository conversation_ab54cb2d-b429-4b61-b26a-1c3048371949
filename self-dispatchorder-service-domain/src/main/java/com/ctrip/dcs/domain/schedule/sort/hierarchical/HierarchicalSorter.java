package com.ctrip.dcs.domain.schedule.sort.hierarchical;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator.CategoryScoreCalculator;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator.GradeCalculator;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.HierarchicalSortConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortCategory;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.CategoryScore;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.HierarchicalSortResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分层排序器
 * 实现基于等级和GPA的分层排序逻辑
 * 
 * <AUTHOR>
 */
@Component
public class HierarchicalSorter {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSorter.class);
    
    @Autowired
    private CategoryScoreCalculator categoryScoreCalculator;
    
    @Autowired
    private GradeCalculator gradeCalculator;
    
    /**
     * 执行分层排序
     * 
     * @param sortModels 待排序的司机模型列表
     * @param sortContext 排序上下文
     * @param config 分层排序配置
     * @param preference 排序偏好
     * @param featureMap 特征项映射
     * @return 排序后的结果列表
     */
    public List<HierarchicalSortResult> sort(List<SortModel> sortModels,
                                           SortContext sortContext,
                                           HierarchicalSortConfig config,
                                           SortPreference preference,
                                           Map<String, Feature> featureMap) {
        if (sortModels == null || sortModels.isEmpty()) {
            logger.warn("待排序的司机列表为空");
            return Collections.emptyList();
        }
        
        if (!config.isEffectivelyEnabled()) {
            logger.warn("分层排序功能已禁用");
            return Collections.emptyList();
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始执行分层排序，司机数量={}，偏好={}", sortModels.size(), preference);
            
            // 并行计算每个司机的排序结果
            List<HierarchicalSortResult> results = calculateSortResults(
                sortModels, sortContext, config, preference, featureMap);
            
            // 排序：先按等级，再按GPA分数，最后按最终分数
            results.sort(this::compareSortResults);
            
            // 设置排名
            setRankings(results);
            
            long totalTime = System.currentTimeMillis() - startTime;
            
            logger.info("分层排序完成，耗时={}ms，成功数量={}，失败数量={}",
                       totalTime, 
                       results.stream().mapToInt(r -> r.isEffectivelySuccess() ? 1 : 0).sum(),
                       results.stream().mapToInt(r -> r.isEffectivelySuccess() ? 0 : 1).sum());
            
            // 记录监控指标
            recordMetrics(results, totalTime);
            
            return results;
            
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            logger.error("执行分层排序时发生异常，耗时={}ms", totalTime, e);
            
            // 记录异常指标
            MetricsUtil.recordValue("hierarchical_sort_error_count");
            
            // 返回空结果或降级处理
            return Collections.emptyList();
        }
    }
    
    /**
     * 计算所有司机的排序结果
     * 
     * @param sortModels 司机模型列表
     * @param sortContext 排序上下文
     * @param config 配置
     * @param preference 偏好
     * @param featureMap 特征项映射
     * @return 排序结果列表
     */
    private List<HierarchicalSortResult> calculateSortResults(List<SortModel> sortModels,
                                                            SortContext sortContext,
                                                            HierarchicalSortConfig config,
                                                            SortPreference preference,
                                                            Map<String, Feature> featureMap) {
        // 使用并行流提高计算性能
        return sortModels.parallelStream()
                .map(sortModel -> calculateSingleSortResult(sortModel, sortContext, config, preference, featureMap))
                .collect(Collectors.toList());
    }
    
    /**
     * 计算单个司机的排序结果
     * 
     * @param sortModel 司机模型
     * @param sortContext 排序上下文
     * @param config 配置
     * @param preference 偏好
     * @param featureMap 特征项映射
     * @return 排序结果
     */
    private HierarchicalSortResult calculateSingleSortResult(SortModel sortModel,
                                                           SortContext sortContext,
                                                           HierarchicalSortConfig config,
                                                           SortPreference preference,
                                                           Map<String, Feature> featureMap) {
        long startTime = System.currentTimeMillis();
        Long driverId = sortModel.getModel().getDriver().getDriverId();
        String orderId = sortModel.getModel().getOrder().getDspOrderId();
        
        try {
            // 计算各大类分数
            List<CategoryScore> categoryScores = categoryScoreCalculator.calculateCategoryScores(
                config.getCategoryConfigs(), sortModel, sortContext, featureMap);
            
            // 计算总体GPA分数
            double overallGpaScore = gradeCalculator.calculateOverallGpaScore(
                categoryScores, preference, config.getCustomWeights());
            
            // 计算总体等级
            SortGrade overallGrade = gradeCalculator.calculateOverallGrade(overallGpaScore);
            
            // 计算最终排序分数
            double finalSortScore = gradeCalculator.calculateFinalSortScore(
                overallGpaScore, categoryScores, preference, config.getCustomWeights());
            
            long calculationTime = System.currentTimeMillis() - startTime;
            
            logger.debug("司机{}排序计算完成：等级={}，GPA={}，最终分数={}，耗时={}ms",
                        driverId, overallGrade, overallGpaScore, finalSortScore, calculationTime);
            
            return HierarchicalSortResult.createSuccess(
                driverId, orderId, overallGrade, overallGpaScore, finalSortScore,
                preference, categoryScores, calculationTime);
            
        } catch (Exception e) {
            long calculationTime = System.currentTimeMillis() - startTime;
            logger.error("计算司机{}排序结果时发生异常", driverId, e);
            
            return HierarchicalSortResult.createFailure(
                driverId, orderId, "计算异常：" + e.getMessage(), calculationTime);
        }
    }
    
    /**
     * 比较排序结果
     * 排序规则：
     * 1. 先比较总体等级（A > B > C > D）
     * 2. 等级相同时比较GPA分数（高分优先）
     * 3. GPA相同时比较最终分数（高分优先）
     * 
     * @param result1 结果1
     * @param result2 结果2
     * @return 比较结果
     */
    private int compareSortResults(HierarchicalSortResult result1, HierarchicalSortResult result2) {
        // 失败的结果排在最后
        if (!result1.isEffectivelySuccess() && result2.isEffectivelySuccess()) {
            return 1;
        }
        if (result1.isEffectivelySuccess() && !result2.isEffectivelySuccess()) {
            return -1;
        }
        if (!result1.isEffectivelySuccess() && !result2.isEffectivelySuccess()) {
            return 0;
        }
        
        // 比较总体等级
        SortGrade grade1 = result1.getEffectiveOverallGrade();
        SortGrade grade2 = result2.getEffectiveOverallGrade();
        int gradeComparison = Integer.compare(grade2.getGpaScore(), grade1.getGpaScore());
        if (gradeComparison != 0) {
            return gradeComparison;
        }
        
        // 比较GPA分数
        double gpa1 = result1.getEffectiveOverallGpaScore();
        double gpa2 = result2.getEffectiveOverallGpaScore();
        int gpaComparison = Double.compare(gpa2, gpa1);
        if (gpaComparison != 0) {
            return gpaComparison;
        }
        
        // 比较最终分数
        double score1 = result1.getEffectiveFinalSortScore();
        double score2 = result2.getEffectiveFinalSortScore();
        return Double.compare(score2, score1);
    }
    
    /**
     * 设置排名
     * 
     * @param results 排序结果列表（已排序）
     */
    private void setRankings(List<HierarchicalSortResult> results) {
        for (int i = 0; i < results.size(); i++) {
            results.get(i).setRank(i + 1);
        }
    }
    
    /**
     * 记录监控指标
     * 
     * @param results 排序结果列表
     * @param totalTime 总耗时
     */
    private void recordMetrics(List<HierarchicalSortResult> results, long totalTime) {
        try {
            // 记录总体指标
            MetricsUtil.recordTime("hierarchical_sort_total_time", totalTime);
            MetricsUtil.recordValue("hierarchical_sort_driver_count", results.size());
            
            // 记录成功失败数量
            long successCount = results.stream().filter(HierarchicalSortResult::isEffectivelySuccess).count();
            long failureCount = results.size() - successCount;
            MetricsUtil.recordValue("hierarchical_sort_success_count", successCount);
            MetricsUtil.recordValue("hierarchical_sort_failure_count", failureCount);
            
            // 记录等级分布
            Map<SortGrade, Long> gradeDistribution = results.stream()
                    .filter(HierarchicalSortResult::isEffectivelySuccess)
                    .collect(Collectors.groupingBy(
                            HierarchicalSortResult::getEffectiveOverallGrade,
                            Collectors.counting()));
            
            for (Map.Entry<SortGrade, Long> entry : gradeDistribution.entrySet()) {
                MetricsUtil.recordValue("hierarchical_sort_grade_" + entry.getKey().name().toLowerCase() + "_count", 
                                      entry.getValue());
            }
            
            // 记录平均计算时间
            double avgCalculationTime = results.stream()
                    .mapToLong(HierarchicalSortResult::getEffectiveTotalCalculationTimeMs)
                    .average()
                    .orElse(0.0);
            MetricsUtil.recordTime("hierarchical_sort_avg_calculation_time", (long) avgCalculationTime);
            
        } catch (Exception e) {
            logger.error("记录监控指标时发生异常", e);
        }
    }
}
