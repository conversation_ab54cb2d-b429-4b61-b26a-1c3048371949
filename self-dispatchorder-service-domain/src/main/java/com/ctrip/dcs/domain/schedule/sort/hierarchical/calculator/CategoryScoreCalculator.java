package com.ctrip.dcs.domain.schedule.sort.hierarchical.calculator;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.CategoryConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.config.SubItemConfig;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortGrade;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.CategoryScore;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.SubItemScore;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 大类分数计算器
 * 负责计算各个大类的分数和等级
 * 
 * <AUTHOR>
 */
@Component
public class CategoryScoreCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryScoreCalculator.class);
    
    /**
     * 计算大类分数
     * 
     * @param categoryConfig 大类配置
     * @param sortModel 排序模型
     * @param sortContext 排序上下文
     * @param featureMap 特征项映射
     * @return 大类分数结果
     */
    public CategoryScore calculateCategoryScore(CategoryConfig categoryConfig,
                                              SortModel sortModel,
                                              SortContext sortContext,
                                              Map<String, Feature> featureMap) {
        if (categoryConfig == null || !categoryConfig.isEffectivelyEnabled()) {
            logger.warn("大类配置无效或已禁用：{}", categoryConfig);
            return CategoryScore.createFailure(
                categoryConfig != null ? categoryConfig.getCategory() : null,
                "大类配置无效或已禁用",
                0L
            );
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 计算所有子项分数
            List<SubItemScore> subItemScores = calculateSubItemScores(
                categoryConfig.getSubItems(), sortModel, sortContext, featureMap);
            
            // 计算大类加权平均分数
            double categoryScore = calculateWeightedAverageScore(subItemScores);
            
            // 根据分数确定等级
            SortGrade grade = categoryConfig.getGradeByScore(categoryScore);
            
            long calculationTime = System.currentTimeMillis() - startTime;
            
            logger.info("大类{}分数计算完成：分数={}，等级={}，耗时={}ms",
                       categoryConfig.getCategory(), categoryScore, grade, calculationTime);
            
            return CategoryScore.createSuccess(
                categoryConfig.getCategory(),
                categoryScore,
                grade,
                categoryConfig.getEffectiveWeight(),
                subItemScores,
                calculationTime
            );
            
        } catch (Exception e) {
            long calculationTime = System.currentTimeMillis() - startTime;
            logger.error("计算大类{}分数时发生异常", categoryConfig.getCategory(), e);
            
            return CategoryScore.createFailure(
                categoryConfig.getCategory(),
                "计算异常：" + e.getMessage(),
                calculationTime
            );
        }
    }
    
    /**
     * 计算所有子项分数
     * 
     * @param subItemConfigs 子项配置列表
     * @param sortModel 排序模型
     * @param sortContext 排序上下文
     * @param featureMap 特征项映射
     * @return 子项分数列表
     */
    private List<SubItemScore> calculateSubItemScores(List<SubItemConfig> subItemConfigs,
                                                    SortModel sortModel,
                                                    SortContext sortContext,
                                                    Map<String, Feature> featureMap) {
        if (subItemConfigs == null || subItemConfigs.isEmpty()) {
            logger.warn("子项配置列表为空");
            return Collections.emptyList();
        }
        
        List<SubItemScore> subItemScores = new ArrayList<>();
        
        for (SubItemConfig subItemConfig : subItemConfigs) {
            if (!subItemConfig.isEffectivelyEnabled()) {
                logger.debug("子项{}已禁用，跳过计算", subItemConfig.getItemId());
                continue;
            }
            
            SubItemScore subItemScore = calculateSubItemScore(subItemConfig, sortModel, sortContext, featureMap);
            subItemScores.add(subItemScore);
        }
        
        return subItemScores;
    }
    
    /**
     * 计算单个子项分数
     * 
     * @param subItemConfig 子项配置
     * @param sortModel 排序模型
     * @param sortContext 排序上下文
     * @param featureMap 特征项映射
     * @return 子项分数结果
     */
    private SubItemScore calculateSubItemScore(SubItemConfig subItemConfig,
                                             SortModel sortModel,
                                             SortContext sortContext,
                                             Map<String, Feature> featureMap) {
        long startTime = System.currentTimeMillis();
        String itemId = subItemConfig.getItemId();
        
        try {
            // 获取对应的特征项
            Feature feature = featureMap.get(itemId);
            if (feature == null) {
                logger.warn("未找到子项{}对应的特征项", itemId);
                return SubItemScore.createFailure(
                    itemId,
                    subItemConfig.getDisplayName(),
                    "未找到对应的特征项",
                    System.currentTimeMillis() - startTime
                );
            }
            
            // 计算原始分数
            double rawScore = calculateRawScore(feature, sortModel, sortContext, subItemConfig.getTimeoutMs());
            
            // 归一化分数
            double normalizedScore = subItemConfig.normalize(rawScore);
            
            long calculationTime = System.currentTimeMillis() - startTime;
            
            logger.debug("子项{}计算完成：原始分数={}，归一化分数={}，权重={}，耗时={}ms",
                        itemId, rawScore, normalizedScore, subItemConfig.getEffectiveWeight(), calculationTime);
            
            return SubItemScore.createSuccess(
                itemId,
                subItemConfig.getDisplayName(),
                rawScore,
                normalizedScore,
                subItemConfig.getEffectiveWeight(),
                calculationTime
            );
            
        } catch (Exception e) {
            long calculationTime = System.currentTimeMillis() - startTime;
            logger.error("计算子项{}分数时发生异常", itemId, e);
            
            return SubItemScore.createFailure(
                itemId,
                subItemConfig.getDisplayName(),
                "计算异常：" + e.getMessage(),
                calculationTime
            );
        }
    }
    
    /**
     * 计算原始分数
     * 调用现有的Feature计算逻辑
     * 
     * @param feature 特征项
     * @param sortModel 排序模型
     * @param sortContext 排序上下文
     * @param timeoutMs 超时时间
     * @return 原始分数
     */
    private double calculateRawScore(Feature feature, SortModel sortModel, 
                                   SortContext sortContext, Long timeoutMs) {
        try {
            // 使用CompletableFuture实现超时控制
            CompletableFuture<Double> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 调用现有的特征项计算逻辑
                    List<Value> values = feature.value(Collections.singletonList(sortModel), sortContext);
                    if (values != null && !values.isEmpty()) {
                        Value value = values.get(0);
                        return value.getValue();
                    }
                    return 0.0;
                } catch (Exception e) {
                    logger.error("特征项计算异常", e);
                    throw new RuntimeException(e);
                }
            });
            
            // 等待结果，支持超时
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
            
        } catch (Exception e) {
            logger.error("计算原始分数时发生异常", e);
            return 0.0;
        }
    }
    
    /**
     * 计算加权平均分数
     * 
     * @param subItemScores 子项分数列表
     * @return 加权平均分数（0-100）
     */
    private double calculateWeightedAverageScore(List<SubItemScore> subItemScores) {
        if (subItemScores == null || subItemScores.isEmpty()) {
            return 0.0;
        }
        
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;
        
        for (SubItemScore subItemScore : subItemScores) {
            if (!subItemScore.isEffectivelySuccess()) {
                logger.debug("子项{}计算失败，跳过加权计算", subItemScore.getItemId());
                continue;
            }
            
            double normalizedScore = subItemScore.getEffectiveNormalizedScore();
            double weight = subItemScore.getEffectiveWeight();
            
            totalWeightedScore += normalizedScore * weight * 100; // 转换为百分制
            totalWeight += weight;
        }
        
        if (totalWeight == 0.0) {
            logger.warn("总权重为0，无法计算加权平均分数");
            return 0.0;
        }
        
        return totalWeightedScore / totalWeight;
    }
    
    /**
     * 批量计算多个大类分数
     * 支持并行计算以提高性能
     * 
     * @param categoryConfigs 大类配置列表
     * @param sortModel 排序模型
     * @param sortContext 排序上下文
     * @param featureMap 特征项映射
     * @return 大类分数结果列表
     */
    public List<CategoryScore> calculateCategoryScores(List<CategoryConfig> categoryConfigs,
                                                     SortModel sortModel,
                                                     SortContext sortContext,
                                                     Map<String, Feature> featureMap) {
        if (categoryConfigs == null || categoryConfigs.isEmpty()) {
            logger.warn("大类配置列表为空");
            return Collections.emptyList();
        }
        
        // 并行计算各大类分数
        return categoryConfigs.parallelStream()
                .filter(config -> config != null && config.isEffectivelyEnabled())
                .map(config -> calculateCategoryScore(config, sortModel, sortContext, featureMap))
                .collect(Collectors.toList());
    }
}
