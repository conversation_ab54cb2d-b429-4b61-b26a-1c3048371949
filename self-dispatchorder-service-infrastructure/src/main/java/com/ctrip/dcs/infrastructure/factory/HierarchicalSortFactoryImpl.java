package com.ctrip.dcs.infrastructure.factory;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.factory.SortFactory;
import com.ctrip.dcs.domain.schedule.sort.SortConfig;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.enums.SortPreference;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.result.HierarchicalSortResult;
import com.ctrip.dcs.domain.schedule.sort.hierarchical.service.HierarchicalSortService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分层排序工厂实现类
 * 扩展现有的排序工厂，支持分层排序功能
 * 
 * <AUTHOR>
 */
@Component
public class HierarchicalSortFactoryImpl {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSortFactoryImpl.class);
    
    @Autowired
    private HierarchicalSortService hierarchicalSortService;
    
    @Autowired
    private SortFactory originalSortFactory;
    
    /**
     * 创建增强的排序器
     * 该排序器会根据配置决定使用分层排序还是原始排序
     * 
     * @param config 排序配置
     * @param featureMap 特征项映射
     * @return 增强的排序器
     */
    public EnhancedSorter createEnhancedSorter(SortConfig config, Map<String, Feature> featureMap) {
        // 创建原始排序器作为降级方案
        Sorter originalSorter = originalSortFactory.create(config);
        
        return new EnhancedSorter(originalSorter, hierarchicalSortService, featureMap);
    }
    
    /**
     * 增强的排序器类
     * 集成分层排序和原始排序功能
     */
    public static class EnhancedSorter extends Sorter {
        
        private final Sorter originalSorter;
        private final HierarchicalSortService hierarchicalSortService;
        private final Map<String, Feature> featureMap;
        
        public EnhancedSorter(Sorter originalSorter, 
                            HierarchicalSortService hierarchicalSortService,
                            Map<String, Feature> featureMap) {
            super(originalSorter.getScorer());
            this.originalSorter = originalSorter;
            this.hierarchicalSortService = hierarchicalSortService;
            this.featureMap = featureMap;
            
            // 复制原始排序器的特征项
            this.features.addAll(originalSorter.getFeatures());
        }
        
        @Override
        public List<SortModel> sort(List<SortModel> models, SortContext context) {
            if (models == null || models.isEmpty()) {
                return Collections.emptyList();
            }
            
            try {
                // 提取订单和司机信息
                DspOrderVO order = context.getDspOrder();
                List<DriverVO> drivers = models.stream()
                        .map(model -> model.getModel().getDriver())
                        .collect(Collectors.toList());
                
                // 尝试使用分层排序
                List<HierarchicalSortResult> hierarchicalResults = hierarchicalSortService.sort(
                        order, drivers, context, null, featureMap);
                
                if (hierarchicalResults != null && !hierarchicalResults.isEmpty()) {
                    // 分层排序成功，转换结果
                    return convertHierarchicalResults(hierarchicalResults, models);
                } else {
                    // 分层排序失败或不适用，使用原始排序
                    logger.info("使用原始排序作为降级方案：订单={}，司机数量={}", 
                               order.getDspOrderId(), models.size());
                    return originalSorter.sort(models, context);
                }
                
            } catch (Exception e) {
                logger.error("增强排序器执行异常，降级到原始排序", e);
                return originalSorter.sort(models, context);
            }
        }
        
        /**
         * 将分层排序结果转换为SortModel列表
         * 
         * @param hierarchicalResults 分层排序结果
         * @param originalModels 原始模型列表
         * @return 转换后的SortModel列表
         */
        private List<SortModel> convertHierarchicalResults(List<HierarchicalSortResult> hierarchicalResults,
                                                          List<SortModel> originalModels) {
            // 创建司机ID到SortModel的映射
            Map<Long, SortModel> modelMap = originalModels.stream()
                    .collect(Collectors.toMap(
                            model -> model.getModel().getDriver().getDriverId(),
                            model -> model
                    ));
            
            // 按分层排序结果顺序重新排列SortModel
            List<SortModel> sortedModels = hierarchicalResults.stream()
                    .filter(result -> result.getDriverId() != null)
                    .map(result -> {
                        SortModel model = modelMap.get(result.getDriverId());
                        if (model != null) {
                            // 设置分层排序的分数
                            model.setScore(result.getEffectiveFinalSortScore());
                            
                            // 添加分层排序的详细信息到调试信息中
                            addHierarchicalDebugInfo(model, result);
                        }
                        return model;
                    })
                    .filter(model -> model != null)
                    .collect(Collectors.toList());
            
            // 添加未在分层排序结果中的司机（排在最后）
            List<Long> sortedDriverIds = hierarchicalResults.stream()
                    .map(HierarchicalSortResult::getDriverId)
                    .collect(Collectors.toList());
            
            List<SortModel> remainingModels = originalModels.stream()
                    .filter(model -> !sortedDriverIds.contains(model.getModel().getDriver().getDriverId()))
                    .collect(Collectors.toList());
            
            sortedModels.addAll(remainingModels);
            
            return sortedModels;
        }
        
        /**
         * 添加分层排序的调试信息到SortModel
         * 
         * @param model SortModel对象
         * @param result 分层排序结果
         */
        private void addHierarchicalDebugInfo(SortModel model, HierarchicalSortResult result) {
            try {
                // 这里可以将分层排序的详细信息添加到model的调试信息中
                // 由于SortModel没有直接的调试信息字段，我们可以通过其他方式记录
                
                logger.debug("司机{}分层排序详情：等级={}，GPA={}，最终分数={}，排名={}",
                           result.getDriverId(),
                           result.getEffectiveOverallGrade(),
                           result.getEffectiveOverallGpaScore(),
                           result.getEffectiveFinalSortScore(),
                           result.getEffectiveRank());
                
            } catch (Exception e) {
                logger.error("添加分层排序调试信息时发生异常", e);
            }
        }
    }
    
    /**
     * 创建指定偏好的排序器
     * 
     * @param config 排序配置
     * @param preference 排序偏好
     * @param featureMap 特征项映射
     * @return 排序器
     */
    public PreferenceSorter createPreferenceSorter(SortConfig config, 
                                                  SortPreference preference,
                                                  Map<String, Feature> featureMap) {
        Sorter originalSorter = originalSortFactory.create(config);
        return new PreferenceSorter(originalSorter, hierarchicalSortService, featureMap, preference);
    }
    
    /**
     * 偏好排序器类
     * 支持指定排序偏好的排序器
     */
    public static class PreferenceSorter extends EnhancedSorter {
        
        private final SortPreference preference;
        
        public PreferenceSorter(Sorter originalSorter,
                              HierarchicalSortService hierarchicalSortService,
                              Map<String, Feature> featureMap,
                              SortPreference preference) {
            super(originalSorter, hierarchicalSortService, featureMap);
            this.preference = preference;
        }
        
        @Override
        public List<SortModel> sort(List<SortModel> models, SortContext context) {
            if (models == null || models.isEmpty()) {
                return Collections.emptyList();
            }
            
            try {
                // 提取订单和司机信息
                DspOrderVO order = context.getDspOrder();
                List<DriverVO> drivers = models.stream()
                        .map(model -> model.getModel().getDriver())
                        .collect(Collectors.toList());
                
                // 使用指定偏好进行分层排序
                List<HierarchicalSortResult> hierarchicalResults = hierarchicalSortService.sort(
                        order, drivers, context, preference, featureMap);
                
                if (hierarchicalResults != null && !hierarchicalResults.isEmpty()) {
                    return convertHierarchicalResults(hierarchicalResults, models);
                } else {
                    logger.info("偏好排序器降级到原始排序：偏好={}，订单={}，司机数量={}", 
                               preference, order.getDspOrderId(), models.size());
                    return originalSorter.sort(models, context);
                }
                
            } catch (Exception e) {
                logger.error("偏好排序器执行异常，降级到原始排序：偏好={}", preference, e);
                return originalSorter.sort(models, context);
            }
        }
        
        public SortPreference getPreference() {
            return preference;
        }
    }
}
